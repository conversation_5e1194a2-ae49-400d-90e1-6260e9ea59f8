import requests
import json
import time
import os

# 设置浏览器ID
browerid = "6884965a390fea598af8b071"

brurl = 'http://127.0.0.1:3001/auto/openBrower'
headers = {
    'Content-Type': 'application/json'
}
payload = json.dumps({
    "browerid": browerid
})

project_response = requests.request("POST", brurl, headers=headers, data=payload)
time.sleep(22)
os.system('taskkill /im chrome.exe /F')
time.sleep(1)
project_response = requests.request("POST", brurl, headers=headers, data=payload)
time.sleep(22)
os.system('taskkill /im chrome.exe /F')
time.sleep(1)

sql3dburl = f"http://127.0.0.1:3001/sql3db/sql3GetBrUn?browerid={browerid}"

projec = requests.request("GET", sql3dburl, headers=headers)

print("API响应状态码:", projec.status_code)
print("API响应内容:")
print(projec.text)

try:
    projson = json.loads(projec.text)
    print(f"\nAPI返回码: {projson.get('code')}")
    print(f"API描述: {projson.get('desc')}")
    
    if projson.get("code") == 0 and "data" in projson:
        prodata = projson["data"]
        print("\n浏览器数据获取成功:")
        print(f"浏览器ID: {prodata.get('browerid')}")
        print(f"浏览器名称: {prodata.get('browername')}")
        print(f"网站URL: {prodata.get('siteurl')}")
        
        cookie = prodata.get('cookie')
        if cookie:
            print(f"\n获取到的cookie数据:")
            # 解析cookie JSON字符串
            cookie_list = json.loads(cookie)
            print(f"Cookie数量: {len(cookie_list)}")
            
            # 转换为标准HTTP Cookie格式
            cookie_pairs = []
            for cookie_item in cookie_list:
                name = cookie_item.get('name', '')
                value = cookie_item.get('value', '')
                if name and value:  # 只包含有名称和值的cookie
                    cookie_pairs.append(f"{name}={value}")
            
            # 输出标准Cookie格式
            cookie_string = "; ".join(cookie_pairs)
            print(f"\n标准Cookie格式:")
            print(cookie_string)
            
            # 保存到文件
            with open('cookies_output.txt', 'w', encoding='utf-8') as f:
                f.write(cookie_string)
            print(f"\nCookie已保存到 cookies_output.txt 文件")
        else:
            print("cookie数据为空")
    else:
        print(f"API返回错误: {projson.get('desc', '未知错误')}")
        
except json.JSONDecodeError as e:
    print(f"JSON解析错误: {e}")
except Exception as e:
    print(f"处理过程中出现错误: {e}")
