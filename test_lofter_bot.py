#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 lofter_bot.py 的关键函数
"""

import re
import html

def extract_lofter_url(text: str) -> str:
    """提取Lofter链接（支持文章链接和合集链接）"""
    # 先尝试匹配文章链接
    article_pattern = r'https?://[\w-]+\.lofter\.com/post/[a-f0-9]+_[a-f0-9]+(?:\?[^\s]*)?'
    match = re.search(article_pattern, text)
    if match:
        return match.group(0)
    
    # 再尝试匹配合集链接
    collection_pattern = r'https?://www\.lofter\.com/front/blog/collection/share\?collectionId=\d+(?:&[^\s]*)?'
    match = re.search(collection_pattern, text)
    if match:
        return match.group(0)
    
    return None

def clean_html_content(content: str) -> str:
    """清理HTML内容"""
    if not content:
        return ""

    # HTML解码
    content = html.unescape(content)

    # 移除多余的空白行
    lines = [line.strip() for line in content.splitlines() if line.strip()]
    content = '\n'.join(lines)

    # 移除重复的换行
    content = re.sub(r'\n{3,}', '\n\n', content)

    return content.strip()

def test_url_extraction():
    """测试URL提取功能"""
    print("=== 测试URL提取功能 ===")
    
    # 测试文章链接
    article_urls = [
        "https://dmscdj.lofter.com/post/84bc89fd_2be5a7548?incantation=rzD5Hc6Y0MGF",
        "https://example.lofter.com/post/12345678_abcdef90",
        "看看这个文章 https://test.lofter.com/post/aabbccdd_11223344 很不错"
    ]
    
    for url_text in article_urls:
        result = extract_lofter_url(url_text)
        print(f"输入: {url_text}")
        print(f"提取结果: {result}")
        print(f"是否为文章链接: {'是' if result and '/post/' in result else '否'}")
        print()
    
    # 测试合集链接
    collection_urls = [
        "https://www.lofter.com/front/blog/collection/share?collectionId=23147679&incantation=hjbly2PvNNmM",
        "https://www.lofter.com/front/blog/collection/share?collectionId=12345678",
        "分享一个合集 https://www.lofter.com/front/blog/collection/share?collectionId=99999999&test=1 给大家"
    ]
    
    for url_text in collection_urls:
        result = extract_lofter_url(url_text)
        print(f"输入: {url_text}")
        print(f"提取结果: {result}")
        print(f"是否为合集链接: {'是' if result and 'collection/share' in result else '否'}")
        print()

def test_link_detection():
    """测试链接类型检测"""
    print("=== 测试链接类型检测 ===")
    
    test_urls = [
        "https://dmscdj.lofter.com/post/84bc89fd_2be5a7548",
        "https://www.lofter.com/front/blog/collection/share?collectionId=23147679",
        "https://invalid.url.com/test"
    ]
    
    for url in test_urls:
        print(f"URL: {url}")
        if 'lofter.com/front/blog/collection/share' in url:
            print("类型: 合集链接")
        elif re.match(r'https://[\w-]+\.lofter\.com/post/[a-f0-9]+_[a-f0-9]+', url):
            print("类型: 文章链接")
        else:
            print("类型: 未知链接")
        print()

def test_content_cleaning():
    """测试内容清理功能"""
    print("=== 测试内容清理功能 ===")
    
    test_content = """
    <p>这是一段测试内容</p>
    <br>
    <div>包含HTML标签的内容</div>
    
    
    
    多余的空行
    
    &lt;转义字符&gt; &amp; &quot;引号&quot;
    """
    
    cleaned = clean_html_content(test_content)
    print("原始内容:")
    print(repr(test_content))
    print("\n清理后内容:")
    print(repr(cleaned))
    print("\n显示效果:")
    print(cleaned)

if __name__ == "__main__":
    test_url_extraction()
    test_link_detection()
    test_content_cleaning()
    print("✅ 所有测试完成")
