#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lofter文章下载器 - 定制版
内置Cookie，支持按作者搜索和下载文章
"""

import requests
import json
import os
import re
import time
from urllib.parse import quote, unquote
from typing import Dict, List, Optional, Tuple
import html
from datetime import datetime


class LofterDownloader:
    # ==================== 内置Cookie配置 ====================
    LOFTER_COOKIE_KEY = "LOFTER-PHONE-LOGIN-AUTH"
    LOFTER_COOKIE_VALUE = "JxNkxwh5V60_c2ha0_61GMAdR9FTtoGpOaOMvp3akrxrfTq1uLuDhIO47vTZEqmHSQ21n_2u70k1QUdfXgS9SFDB_zB32lo-"
    # ==================== 配置结束 ====================

    def __init__(self):
        self.session = requests.Session()
        self.cookies = {}
        self.headers = {
            "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        self.base_url = "https://newsmiss.lofter.com"
        self.search_base_url = "https://api.lofter.com/newsearch/"

        # 自动加载内置Cookie
        self._load_builtin_cookies()

    def _load_builtin_cookies(self) -> bool:
        """加载内置Cookie"""
        try:
            if self.LOFTER_COOKIE_KEY and self.LOFTER_COOKIE_VALUE:
                self.cookies[self.LOFTER_COOKIE_KEY] = self.LOFTER_COOKIE_VALUE
                self.headers[self.LOFTER_COOKIE_KEY] = self.LOFTER_COOKIE_VALUE
                self.session.cookies.update(self.cookies)
                print(f"✅ 内置Cookie加载成功: {self.LOFTER_COOKIE_KEY}")
                return True
            else:
                print("❌ 内置Cookie未配置")
                return False
        except Exception as e:
            print(f"❌ 内置Cookie加载失败: {e}")
            return False

    def load_cookies(self, cookie_file: str = "cookies.txt") -> bool:
        """从文件加载cookies"""
        try:
            if os.path.exists(cookie_file):
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    cookie_str = f.read().strip()

                # 跳过注释行和空行
                lines = cookie_str.split('\n')
                cookie_str = ""
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        cookie_str = line
                        break

                if not cookie_str:
                    print("❌ Cookie文件中没有找到有效的Cookie值")
                    return False

                # 解析cookie字符串
                for cookie in cookie_str.split(';'):
                    if '=' in cookie:
                        key, value = cookie.strip().split('=', 1)
                        self.cookies[key] = value

                # 查找LOFTER登录认证cookie
                lofter_auth = None
                for key, value in self.cookies.items():
                    if key.startswith('LOFTER-') and key.endswith('-LOGIN-AUTH'):
                        lofter_auth = {key: value}
                        break

                if lofter_auth:
                    self.headers.update(lofter_auth)
                    self.session.cookies.update(self.cookies)
                    print("✅ Cookies加载成功")
                    print(f"🔑 使用认证: {list(lofter_auth.keys())[0]}")
                    return True
                else:
                    print("❌ 未找到有效的LOFTER登录认证cookie")
                    print("💡 请确保Cookie中包含类似 'LOFTER-XXXXXX-LOGIN-AUTH' 的认证信息")
                    return False
            else:
                print(f"❌ Cookie文件 {cookie_file} 不存在")
                return False
        except Exception as e:
            print(f"❌ 加载cookies失败: {e}")
            return False
    
    def save_cookies(self, cookie_str: str, cookie_file: str = "cookies.txt"):
        """保存cookies到文件"""
        try:
            with open(cookie_file, 'w', encoding='utf-8') as f:
                f.write(cookie_str)
            print(f"✅ Cookies已保存到 {cookie_file}")
        except Exception as e:
            print(f"❌ 保存cookies失败: {e}")
    
    def search_collections(self, keyword: str = "#合集", page: int = 1) -> Optional[Dict]:
        """搜索合集，返回合集信息和文章列表"""
        try:
            # 根据JSON配置，使用正确的合集搜索API
            offset = (page - 1) * 20

            # 如果关键词不以#开头，自动添加
            if not keyword.startswith('#') and not keyword.startswith('＃'):
                search_keyword = f"#{keyword}"
            else:
                search_keyword = keyword

            # 使用合集搜索API
            search_url = f"{self.search_base_url}collection.json?key={quote(search_keyword.lstrip('#＃'))}&limit=20&offset={offset}"

            print(f"🔍 搜索合集: {search_keyword}")
            print(f"📡 请求URL: {search_url}")

            response = self.session.get(search_url, headers=self.headers)

            print(f"📊 响应状态码: {response.status_code}")

            if response.status_code == 401:
                print("❌ 认证失败，请检查Cookie是否有效")
                return None
            elif response.status_code == 403:
                print("❌ 访问被拒绝，可能需要重新登录")
                return None

            response.raise_for_status()

            try:
                data = response.json()
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                print(f"响应内容: {response.text[:200]}...")
                return None

            print(f"📋 响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")

            if 'data' in data and 'collections' in data['data'] and data['data']['collections']:
                collections_count = len(data['data']['collections'])
                print(f"✅ 找到 {collections_count} 个合集")
                return data['data']
            else:
                print("❌ 未找到合集")
                if 'data' in data:
                    print(f"💡 搜索返回的数据结构: {list(data['data'].keys()) if isinstance(data['data'], dict) else data['data']}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 搜索合集失败: {e}")
            return None

    def search_collection_by_name(self, collection_name: str) -> Optional[Dict]:
        """通过合集名称搜索合集"""
        try:
            print(f"🔍 通过名称搜索合集: {collection_name}")

            # 使用合集搜索API
            search_url = f"{self.search_base_url}collection.json?key={quote(collection_name)}&limit=20&offset=0"

            response = self.session.get(search_url, headers=self.headers)

            if response.status_code != 200:
                print(f"❌ 搜索失败，状态码: {response.status_code}")
                return None

            try:
                data = response.json()
                if 'data' in data and 'collections' in data['data'] and data['data']['collections']:
                    collections = data['data']['collections']

                    # 查找最匹配的合集
                    best_match = None
                    best_score = 0

                    for collection in collections:
                        name = collection.get('name', '')
                        # 计算匹配度
                        if collection_name.lower() in name.lower():
                            score = len(collection_name) / len(name) if name else 0
                            if score > best_score:
                                best_score = score
                                best_match = collection

                    if best_match:
                        print(f"✅ 找到匹配合集: {best_match.get('name', '未知合集')}")
                        return best_match
                    else:
                        print("❌ 未找到匹配的合集")
                        return None
                else:
                    print("❌ 搜索结果中没有合集")
                    return None

            except json.JSONDecodeError:
                print("❌ 搜索响应不是有效JSON")
                return None

        except Exception as e:
            print(f"❌ 通过名称搜索合集失败: {e}")
            return None

    def search_author(self, author_name: str, page: int = 1) -> Optional[Dict]:
        """搜索作者，返回作者信息和文章列表"""
        try:
            # 根据JSON配置构建搜索URL
            offset = (page - 1) * 10
            search_url = f"{self.search_base_url}blog.json?key={quote(author_name)}&limit=10&offset={offset}"

            print(f"🔍 搜索作者: {author_name}")
            print(f"📡 请求URL: {search_url}")

            response = self.session.get(search_url, headers=self.headers)

            print(f"📊 响应状态码: {response.status_code}")

            if response.status_code == 401:
                print("❌ 认证失败，请检查Cookie是否有效")
                return None
            elif response.status_code == 403:
                print("❌ 访问被拒绝，可能需要重新登录")
                return None

            response.raise_for_status()

            try:
                data = response.json()
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                print(f"响应内容: {response.text[:200]}...")
                return None

            print(f"📋 响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")

            if 'data' in data and 'blogs' in data['data'] and data['data']['blogs']:
                blogs_count = len(data['data']['blogs'])
                print(f"✅ 找到 {blogs_count} 个匹配的博客")
                return data['data']
            else:
                print("❌ 未找到该作者")
                if 'data' in data:
                    print(f"💡 搜索返回的数据结构: {list(data['data'].keys()) if isinstance(data['data'], dict) else data['data']}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 搜索作者失败: {e}")
            return None

    def download_collection_from_url(self, collection_url: str) -> Optional[str]:
        """从合集链接下载合集内容"""
        try:
            print(f"📚 开始下载合集: {collection_url}")

            # 解析合集URL获取collectionId
            # URL格式: https://www.lofter.com/front/blog/collection/share?collectionId=23147679&incantation=hjbly2PvNNmM
            import urllib.parse as urlparse
            parsed_url = urlparse.urlparse(collection_url)
            query_params = urlparse.parse_qs(parsed_url.query)

            if 'collectionId' not in query_params:
                print("❌ 无法从URL中提取合集ID")
                return None

            collection_id = query_params['collectionId'][0]
            print(f"🆔 合集ID: {collection_id}")

            # 由于直接通过ID获取合集信息可能失败，我们尝试通过搜索合集名称
            print("💡 提示：如果知道合集名称，可以直接搜索合集名称获得更好的结果")
            print("💡 例如：搜索 'ⅰf线之百变江晚吟' 或其他合集关键词")

            # 先尝试通过ID搜索
            collection_info = self._get_collection_info(collection_id)

            # 如果通过ID搜索失败，提示用户使用合集名称搜索
            if not collection_info:
                print("❌ 通过合集ID无法获取合集信息")
                print("💡 建议使用以下方式：")
                print("1. 返回主菜单，选择 '搜索合集 (#合集)'")
                print("2. 输入合集名称，如：'ⅰf线之百变江晚吟'")
                print("3. 或者输入：'#ⅰf线之百变江晚吟'")
                return None

            # 获取合集中的文章列表
            collection_posts = self._get_collection_posts_by_id(collection_id, collection_info)
            if not collection_posts:
                print("❌ 无法获取合集文章列表")
                return None

            print(f"📖 合集包含 {len(collection_posts)} 篇文章")

            # 询问下载方式
            print("\n📋 合集下载选项:")
            print("1. 📦 下载整个合集为一个文件")
            print("2. 📚 分别下载每篇文章")
            print("3. 📄 仅显示合集信息")

            choice = input("请选择下载方式 (1-3): ").strip()

            if choice == '1':
                # 合并所有文章为一个文件
                return self._download_collection_as_single_file(collection_info, collection_posts)
            elif choice == '2':
                # 分别下载每篇文章
                return self._download_collection_as_separate_files(collection_info, collection_posts)
            elif choice == '3':
                # 仅显示信息
                return self._show_collection_info(collection_info, collection_posts)
            else:
                print("❌ 无效选择")
                return None

        except Exception as e:
            print(f"❌ 合集下载失败: {e}")
            return None

    def _get_collection_info(self, collection_id: str) -> Optional[Dict]:
        """获取合集基本信息"""
        try:
            # 根据JSON配置，使用正确的API调用方式
            # 首先通过搜索API获取合集信息
            search_url = f"https://api.lofter.com/newsearch/collection.json?key={collection_id}&limit=20&offset=0"

            print(f"🔍 搜索合集信息: {search_url}")
            response = self.session.get(search_url, headers=self.headers)

            if response.status_code != 200:
                print(f"❌ 搜索合集失败，状态码: {response.status_code}")
                return None

            try:
                result = response.json()
                print(f"📊 搜索响应结构: {list(result.keys()) if isinstance(result, dict) else type(result)}")

                # 检查不同层级的collections字段
                collections = None
                if 'data' in result and 'collections' in result['data']:
                    collections = result['data']['collections']
                    print(f"🔍 data.collections字段: 类型={type(collections)}, 长度={len(collections) if collections else 0}")
                elif 'collections' in result:
                    collections = result['collections']
                    print(f"🔍 collections字段: 类型={type(collections)}, 长度={len(collections) if collections else 0}")

                if collections and len(collections) > 0:
                    print(f"✅ 找到 {len(collections)} 个合集")

                    # 显示所有找到的合集
                    for i, collection in enumerate(collections):
                        print(f"  合集{i+1}: ID={collection.get('id')}, 名称={collection.get('name', '未知')}")

                    # 查找匹配的合集
                    target_collection = None
                    for collection in collections:
                        if str(collection.get('id', '')) == collection_id:
                            target_collection = collection
                            print(f"✅ 找到ID匹配的合集: {collection.get('name', '未知合集')}")
                            break

                    if not target_collection and collections:
                        # 如果没有精确匹配，使用第一个结果
                        target_collection = collections[0]
                        print("⚠️ 没有ID匹配，使用第一个搜索结果")

                    if target_collection:
                        print(f"✅ 返回合集信息")
                        print(f"📝 合集名称: {target_collection.get('name', '未知合集')}")
                        print(f"👤 创建者: {target_collection.get('blogNickName', '未知作者')}")
                        print(f"📊 文章数量: {target_collection.get('postCount', 0)}")
                        print(f"🏠 博客ID: {target_collection.get('blogId', 'N/A')}")
                        print(f"🌐 博客名: {target_collection.get('blogName', 'N/A')}")
                        return target_collection
                    else:
                        print("❌ 未找到匹配的合集")
                        return None
                else:
                    print("❌ 搜索结果中没有合集数据或合集列表为空")
                    if 'data' in result:
                        data = result['data']
                        print(f"💡 data结构: {list(data.keys()) if isinstance(data, dict) else data}")
                        if 'collections' in data:
                            print(f"💡 data.collections内容: {data['collections']}")
                    if 'collections' in result:
                        print(f"💡 root.collections内容: {result['collections']}")
                    print(f"💡 完整响应结构: {list(result.keys())}")

                    # 尝试通过合集名称搜索作为备选方案
                    print("🔄 尝试通过合集名称搜索...")
                    name_result = self.search_collection_by_name("ⅰf线之百变江晚吟")
                    if name_result:
                        print("✅ 通过名称搜索找到合集")
                        return name_result

                    return None

            except json.JSONDecodeError:
                print("❌ 搜索响应不是有效JSON")
                print(f"响应内容: {response.text[:200]}...")
                return None

        except Exception as e:
            print(f"❌ 获取合集信息异常: {e}")
            return None

    def _get_collection_posts_by_id(self, collection_id: str, collection_info: Dict = None) -> Optional[List[Dict]]:
        """通过合集ID获取合集中的文章列表"""
        try:
            if not collection_info:
                print("❌ 需要合集信息来获取文章列表")
                return None

            # 从合集信息中获取必要参数
            blog_id = collection_info.get('blogId')
            blog_name = collection_info.get('blogName')

            if not blog_id or not blog_name:
                print("❌ 合集信息中缺少博客ID或博客名")
                print(f"💡 可用字段: {list(collection_info.keys())}")
                return None

            print(f"📚 获取合集文章: blogId={blog_id}, blogName={blog_name}")

            # 根据JSON配置使用正确的API
            api_url = "https://api.lofter.com/v1.1/postCollection.api?product=lofter-android-7.4.4"

            # 构建请求体
            post_data = {
                "blogdomain": f"{blog_name}.lofter.com",
                "method": "getCollectionSimple",
                "offset": "0",
                "limit": "2000",
                "blogid": str(blog_id),
                "collectionid": str(collection_id),
                "order": "1"
            }

            print(f"📡 请求合集文章API: {api_url}")
            print(f"📝 请求参数: {post_data}")

            response = self.session.post(api_url, data=post_data, headers=self.headers)

            if response.status_code != 200:
                print(f"❌ 获取合集文章失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text[:200]}...")
                return None

            try:
                result = response.json()
                print(f"📊 合集文章响应结构: {list(result.keys()) if isinstance(result, dict) else type(result)}")

                # 详细调试信息
                if 'response' in result:
                    response_data = result['response']
                    print(f"🔍 response字段类型: {type(response_data)}")
                    if isinstance(response_data, dict):
                        print(f"🔍 response内容: {list(response_data.keys())}")
                        if 'items' in response_data:
                            items = response_data['items']
                            print(f"🔍 items字段存在，类型: {type(items)}, 内容: {items if not items or len(str(items)) < 200 else str(items)[:200] + '...'}")
                        else:
                            print("🔍 response中没有items字段")

                # 根据实际API响应，文章列表在 response.items 中
                if 'response' in result and 'items' in result['response']:
                    items = result['response']['items']
                    print(f"🔍 找到items字段，类型: {type(items)}, 长度: {len(items) if items else 0}")

                    if items:
                        print(f"✅ 成功获取合集中的 {len(items)} 篇文章")
                        # 显示第一个item的结构用于调试
                        if len(items) > 0:
                            print(f"📋 第一个item结构: {list(items[0].keys()) if isinstance(items[0], dict) else type(items[0])}")
                        return items
                    else:
                        print("❌ 合集中没有文章（items为空）")
                        return None
                elif 'response' in result and 'posts' in result['response']:
                    # 备选：有些API可能返回posts字段
                    posts = result['response']['posts']
                    if posts:
                        print(f"✅ 成功获取合集中的 {len(posts)} 篇文章")
                        return posts
                    else:
                        print("❌ 合集中没有文章")
                        return None
                else:
                    print("❌ 合集文章响应格式错误")
                    if 'response' in result:
                        print(f"💡 response结构: {list(result['response'].keys()) if isinstance(result['response'], dict) else result['response']}")
                    return None

            except json.JSONDecodeError:
                print("❌ 合集文章响应不是有效JSON")
                print(f"响应内容: {response.text[:200]}...")
                return None

        except Exception as e:
            print(f"❌ 获取合集文章异常: {e}")
            return None

    def get_collection_posts(self, search_data: Dict) -> Optional[List[Dict]]:
        """从搜索结果中提取合集文章列表"""
        try:
            # 检查是否是合集搜索结果
            if 'collections' in search_data:
                print("📚 处理合集搜索结果...")
                collections = search_data['collections']

                if not collections:
                    print("❌ 搜索结果中没有合集")
                    return None

                # 显示找到的合集
                print(f"✅ 找到 {len(collections)} 个合集:")
                for i, collection in enumerate(collections, 1):
                    name = collection.get('name', '未知合集')
                    author = collection.get('blogNickName', '未知作者')
                    post_count = collection.get('postCount', 0)
                    print(f"  {i}. {name} (作者: {author}, {post_count}篇文章)")

                return collections

            # 处理文章搜索结果（原有逻辑）
            elif 'posts' in search_data:
                posts = search_data['posts']
                print(f"📚 处理 {len(posts)} 个文章...")

                # 过滤和处理合集文章
                collection_posts = []
                for post in posts:
                    if 'post' in post and isinstance(post['post'], dict):
                        post_data = post['post']

                        # 检查是否包含合集标签
                        title = post_data.get('title', '')
                        digest = post_data.get('digest', '')
                        content = post_data.get('content', '')

                        if '#合集' in title or '#合集' in digest or '#合集' in content:
                            collection_posts.append(post)
                            print(f"✅ 找到合集文章: {title or digest or '无标题'}")

                if collection_posts:
                    print(f"🎯 共找到 {len(collection_posts)} 个合集文章")
                    return collection_posts
                else:
                    print("❌ 没有找到包含 '#合集' 标签的文章")
                    return None
            else:
                print("❌ 搜索结果格式不正确")
                print(f"💡 可用字段: {list(search_data.keys())}")
                return None

        except Exception as e:
            print(f"❌ 处理合集文章失败: {e}")
            return None

    def _download_collection_as_single_file(self, collection_info: Dict, collection_posts: List[Dict]) -> Optional[str]:
        """将合集下载为单个文件"""
        try:
            collection_title = collection_info.get('title', '未知合集')
            collection_author = collection_info.get('blogInfo', {}).get('blogNickName', '未知作者')

            print(f"📦 开始合并合集: {collection_title}")

            # 构建合集内容
            content_parts = []
            content_parts.append(f"合集标题: {collection_title}")
            content_parts.append(f"创建者: {collection_author}")
            content_parts.append(f"文章数量: {len(collection_posts)}")
            content_parts.append(f"下载时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            content_parts.append("=" * 80)
            content_parts.append("")

            success_count = 0
            for i, post in enumerate(collection_posts, 1):
                try:
                    if 'post' not in post or not isinstance(post['post'], dict):
                        continue

                    post_data = post['post']
                    title = post_data.get('title', post_data.get('digest', f'文章{i}'))
                    author = post_data.get('blogInfo', {}).get('blogNickName', '未知作者')

                    print(f"📖 [{i}/{len(collection_posts)}] 处理文章: {title}")

                    # 提取文章内容
                    article_content = self.extract_post_content(post_data)
                    if article_content:
                        content_parts.append(f"第 {i} 篇文章")
                        content_parts.append(f"标题: {title}")
                        content_parts.append(f"作者: {author}")
                        content_parts.append("-" * 60)
                        content_parts.append(article_content)
                        content_parts.append("")
                        content_parts.append("=" * 80)
                        content_parts.append("")
                        success_count += 1
                    else:
                        print(f"❌ 无法提取文章内容: {title}")

                except Exception as e:
                    print(f"❌ 处理文章失败: {e}")
                    continue

            if success_count > 0:
                # 合并所有内容
                full_content = '\n'.join(content_parts)

                # 保存合集文件
                if self.save_article(f"【合集】{collection_title}", full_content, collection_author):
                    print(f"✅ 合集下载完成！成功处理 {success_count}/{len(collection_posts)} 篇文章")
                    return full_content
                else:
                    print("❌ 合集保存失败")
                    return None
            else:
                print("❌ 没有成功处理任何文章")
                return None

        except Exception as e:
            print(f"❌ 合集合并下载失败: {e}")
            return None

    def _download_collection_as_separate_files(self, collection_info: Dict, collection_posts: List[Dict]) -> Optional[str]:
        """将合集中的文章分别下载为独立文件"""
        try:
            collection_title = collection_info.get('title', '未知合集')
            print(f"📚 开始分别下载合集文章: {collection_title}")

            success_count = 0
            total_count = len(collection_posts)

            for i, post in enumerate(collection_posts, 1):
                try:
                    if 'post' not in post or not isinstance(post['post'], dict):
                        continue

                    post_data = post['post']
                    title = post_data.get('title', post_data.get('digest', f'合集文章{i}'))
                    author = post_data.get('blogInfo', {}).get('blogNickName', '未知作者')

                    print(f"\n📖 [{i}/{total_count}] 下载文章: {title}")

                    # 提取文章内容
                    content = self.extract_post_content(post_data)
                    if content:
                        # 添加合集信息到文章内容
                        full_content = f"所属合集: {collection_title}\n标题: {title}\n作者: {author}\n\n{'-'*50}\n\n{content}"

                        if self.save_article(f"[{collection_title}] {title}", full_content, author):
                            success_count += 1
                            print(f"✅ 文章保存成功")
                        else:
                            print(f"❌ 文章保存失败")
                    else:
                        print(f"❌ 无法提取文章内容")

                except Exception as e:
                    print(f"❌ 下载文章失败: {e}")
                    continue

            result_summary = f"合集下载完成！成功: {success_count}/{total_count}"
            print(f"\n🎉 {result_summary}")
            return result_summary

        except Exception as e:
            print(f"❌ 分别下载合集失败: {e}")
            return None

    def _show_collection_info(self, collection_info: Dict, collection_posts: List[Dict]) -> Optional[str]:
        """显示合集信息"""
        try:
            collection_title = collection_info.get('title', '未知合集')
            collection_author = collection_info.get('blogInfo', {}).get('blogNickName', '未知作者')
            collection_desc = collection_info.get('description', '无描述')

            info_parts = []
            info_parts.append("📚 合集信息")
            info_parts.append("=" * 50)
            info_parts.append(f"合集名称: {collection_title}")
            info_parts.append(f"创建者: {collection_author}")
            info_parts.append(f"文章数量: {len(collection_posts)}")
            info_parts.append(f"合集描述: {collection_desc}")
            info_parts.append("")
            info_parts.append("📖 文章列表:")
            info_parts.append("-" * 50)

            for i, post in enumerate(collection_posts, 1):
                if 'post' in post and isinstance(post['post'], dict):
                    post_data = post['post']
                    title = post_data.get('title', post_data.get('digest', f'文章{i}'))
                    author = post_data.get('blogInfo', {}).get('blogNickName', '未知作者')

                    info_parts.append(f"{i:3d}. {title}")
                    info_parts.append(f"     👤 作者: {author}")

            info_content = '\n'.join(info_parts)
            print(info_content)

            return info_content

        except Exception as e:
            print(f"❌ 显示合集信息失败: {e}")
            return None

    def download_collection_articles(self, collection_posts: List[Dict]) -> bool:
        """下载合集中的所有文章"""
        try:
            if not collection_posts:
                print("❌ 没有合集文章可下载")
                return False

            print(f"🚀 开始下载 {len(collection_posts)} 个合集文章...")

            success_count = 0
            total_count = len(collection_posts)

            for i, post in enumerate(collection_posts, 1):
                try:
                    if 'post' not in post or not isinstance(post['post'], dict):
                        print(f"❌ 第 {i} 个文章数据格式错误")
                        continue

                    post_data = post['post']
                    title = post_data.get('title', post_data.get('digest', f'合集文章_{i}'))
                    author = post_data.get('blogInfo', {}).get('blogNickName', '未知作者')

                    print(f"\n📖 [{i}/{total_count}] 下载文章: {title}")
                    print(f"👤 作者: {author}")

                    # 提取文章内容
                    content = self.extract_post_content(post_data)
                    if not content:
                        print(f"❌ 无法提取文章内容")
                        continue

                    # 保存文章
                    if self.save_article(title, content, author):
                        success_count += 1
                        print(f"✅ 文章保存成功")
                    else:
                        print(f"❌ 文章保存失败")

                except Exception as e:
                    print(f"❌ 下载第 {i} 个文章失败: {e}")
                    continue

            print(f"\n🎉 合集下载完成！")
            print(f"📊 成功: {success_count}/{total_count}")

            return success_count > 0

        except Exception as e:
            print(f"❌ 下载合集文章失败: {e}")
            return False

    def get_author_posts(self, blog_info: Dict) -> Optional[List[Dict]]:
        """获取作者的所有文章列表"""
        try:
            blog_id = blog_info.get('blogId')
            blog_name = blog_info.get('blogName')

            if not blog_id or not blog_name:
                print("❌ 缺少必要的博客信息")
                print(f"💡 博客信息: {blog_info}")
                return None

            print(f"📚 获取作者文章列表...")
            print(f"🆔 博客ID: {blog_id}")
            print(f"📝 博客名: {blog_name}")

            # 根据JSON配置构建获取文章列表的URL
            api_url = "http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-7.4.4"

            post_data = {
                "targetblogid": blog_id,
                "supportposttypes": "1,2,3,4,5,6",
                "blogdomain": f"{blog_name}.lofter.com",
                "offset": "0",
                "method": "getPostLists",
                "postdigestnew": "1",
                "returnData": "1",
                "limit": "500",  # 获取更多文章
                "checkpwd": "1",
                "needgetpoststat": "1"
            }

            print(f"� 请求数据: {post_data}")

            response = self.session.post(api_url, data=post_data, headers=self.headers)

            print(f"📊 响应状态码: {response.status_code}")

            if response.status_code == 401:
                print("❌ 认证失败，请检查Cookie是否有效")
                return None
            elif response.status_code == 403:
                print("❌ 访问被拒绝，可能需要重新登录")
                return None

            response.raise_for_status()

            try:
                data = response.json()
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                print(f"响应内容: {response.text[:200]}...")
                return None

            print(f"📋 响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")

            if 'response' in data and 'posts' in data['response']:
                posts = data['response']['posts']
                print(f"✅ 找到 {len(posts)} 篇文章")
                return posts
            else:
                print("❌ 获取文章列表失败")
                if 'response' in data:
                    print(f"💡 响应结构: {list(data['response'].keys()) if isinstance(data['response'], dict) else data['response']}")
                else:
                    print(f"💡 完整响应: {data}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 获取文章列表失败: {e}")
            return None

    def get_all_author_posts(self, blog_info: Dict) -> Optional[List[Dict]]:
        """获取作者的所有文章列表（分页获取全部文章）"""
        try:
            blog_id = blog_info.get('blogId')
            blog_name = blog_info.get('blogName')

            if not blog_id or not blog_name:
                print("❌ 缺少必要的博客信息")
                return None

            print(f"📚 开始获取作者的全部文章...")
            print(f"🆔 博客ID: {blog_id}")
            print(f"📝 博客名: {blog_name}")

            all_posts = []
            offset = 0
            limit = 500
            page = 1

            while True:
                print(f"📄 正在获取第 {page} 页文章 (偏移量: {offset})...")

                # 根据JSON配置构建获取文章列表的URL
                api_url = "http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-7.4.4"

                post_data = {
                    "targetblogid": blog_id,
                    "supportposttypes": "1,2,3,4,5,6",
                    "blogdomain": f"{blog_name}.lofter.com",
                    "offset": str(offset),
                    "method": "getPostLists",
                    "postdigestnew": "1",
                    "returnData": "1",
                    "limit": str(limit),
                    "checkpwd": "1",
                    "needgetpoststat": "1"
                }

                response = self.session.post(api_url, data=post_data, headers=self.headers)

                if response.status_code == 401:
                    print("❌ 认证失败，请检查Cookie是否有效")
                    return None
                elif response.status_code == 403:
                    print("❌ 访问被拒绝，可能需要重新登录")
                    return None

                response.raise_for_status()

                try:
                    data = response.json()
                except json.JSONDecodeError:
                    print("❌ 响应不是有效的JSON格式")
                    print(f"响应内容: {response.text[:200]}...")
                    break

                # 检查响应数据结构
                if not isinstance(data, dict):
                    print(f"❌ 响应数据不是字典格式: {type(data)}")
                    break

                if 'response' not in data:
                    print("❌ 响应中没有 'response' 字段")
                    print(f"可用字段: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")
                    break

                response_data = data['response']
                if not isinstance(response_data, dict):
                    print(f"❌ response 字段不是字典格式: {type(response_data)}")
                    break

                if 'posts' not in response_data:
                    print("❌ response 中没有 'posts' 字段")
                    print(f"response 可用字段: {list(response_data.keys()) if isinstance(response_data, dict) else 'N/A'}")
                    break

                posts = response_data['posts']
                if posts is None:
                    print(f"📄 第 {page} 页返回 None，没有更多文章")
                    break

                if not isinstance(posts, list):
                    print(f"❌ posts 字段不是列表格式: {type(posts)}")
                    break

                if not posts:
                    # 没有更多文章了
                    print(f"📄 第 {page} 页没有更多文章")
                    break

                all_posts.extend(posts)
                print(f"✅ 第 {page} 页获取到 {len(posts)} 篇文章，累计 {len(all_posts)} 篇")

                # 如果这一页的文章数量少于limit，说明已经是最后一页
                if len(posts) < limit:
                    print(f"📄 已获取完所有文章（最后一页只有 {len(posts)} 篇）")
                    break

                # 准备获取下一页
                offset += limit
                page += 1

                # 移除延时，加快下载速度
                # time.sleep(1)

            if all_posts:
                print(f"🎉 总共获取到 {len(all_posts)} 篇文章")
                return all_posts
            else:
                print("❌ 未获取到任何文章")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 获取文章列表失败: {e}")
            return None

    def get_post_content(self, post_info: Dict) -> Optional[str]:
        """获取单篇文章内容"""
        try:
            # 处理嵌套的post结构
            if 'post' in post_info and isinstance(post_info['post'], dict):
                actual_post = post_info['post']
                blog_id = actual_post.get('blogId')
                post_id = actual_post.get('id')
                title = actual_post.get('title', actual_post.get('digest', '无标题'))

                # 如果已经有完整的content，直接使用
                if actual_post.get('content'):
                    content = actual_post.get('content')
                    author = actual_post.get('blogInfo', {}).get('blogNickName', '未知作者')
                    publish_time = actual_post.get('publishTime', '')

                    if publish_time:
                        try:
                            import datetime
                            publish_time = datetime.datetime.fromtimestamp(int(publish_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                        except:
                            pass

                    # 清理HTML标签
                    content = self.clean_html(content)

                    if not content.strip():
                        print("⚠️ 文章内容为空")
                        return None

                    result = f"标题: {title}\n作者: {author}\n"
                    if publish_time:
                        result += f"发布时间: {publish_time}\n"
                    result += f"\n{'-'*50}\n\n{content}"

                    print(f"✅ 成功获取文章内容 ({len(content)} 字符)")
                    return result
            else:
                # 处理简单的post结构
                blog_id = post_info.get('blogId')
                post_id = post_info.get('id')
                title = post_info.get('title', post_info.get('digest', '无标题'))

            if not blog_id or not post_id:
                print("❌ 缺少文章信息")
                print(f"💡 文章信息: {list(post_info.keys())}")
                return None

            print(f"📖 获取文章内容: {title[:30]}...")
            print(f"🆔 文章ID: {post_id}")
            print(f"🆔 博客ID: {blog_id}")

            # 根据JSON配置构建获取文章详情的URL
            api_url = "https://api.lofter.com/oldapi/post/detail.api?product=lofter-android-7.4.4"

            post_data = f"blogdomain=_blogid_{blog_id}.lofter.com&postid={post_id}"

            print(f"📡 请求数据: {post_data}")

            response = self.session.post(api_url, data=post_data, headers=self.headers)

            print(f"📊 响应状态码: {response.status_code}")

            if response.status_code == 401:
                print("❌ 认证失败，请检查Cookie是否有效")
                return None
            elif response.status_code == 403:
                print("❌ 访问被拒绝，可能需要重新登录")
                return None

            response.raise_for_status()

            try:
                data = response.json()
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                print(f"响应内容: {response.text[:200]}...")
                return None

            print(f"📋 响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")

            if 'response' in data and 'post' in data['response']:
                post_data = data['response']['post']

                # 提取文章内容
                content = post_data.get('content', '')
                title = post_data.get('title', post_data.get('digest', '无标题'))
                author = post_data.get('blogInfo', {}).get('blogNickName', '未知作者')
                publish_time = post_data.get('publishTime', '')

                if publish_time:
                    try:
                        import datetime
                        publish_time = datetime.datetime.fromtimestamp(int(publish_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass

                # 清理HTML标签
                content = self.clean_html(content)

                if not content.strip():
                    print("⚠️ 文章内容为空，可能需要特殊权限访问")
                    return None

                result = f"标题: {title}\n作者: {author}\n"
                if publish_time:
                    result += f"发布时间: {publish_time}\n"
                result += f"\n{'-'*50}\n\n{content}"

                print(f"✅ 成功获取文章内容 ({len(content)} 字符)")
                return result
            else:
                print("❌ 获取文章内容失败")
                if 'response' in data:
                    print(f"💡 响应结构: {list(data['response'].keys()) if isinstance(data['response'], dict) else data['response']}")
                else:
                    print(f"💡 完整响应: {data}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 获取文章内容失败: {e}")
            return None

    def download_from_url(self, article_url: str) -> Optional[str]:
        """智能链接下载：支持文章链接和合集链接"""
        try:
            print(f"🔗 智能下载链接: {article_url}")

            # 检查是否为合集链接
            if 'lofter.com/front/blog/collection/share' in article_url:
                print("📚 检测到合集链接，使用合集下载模式...")
                return self.download_collection_from_url(article_url)

            # 解析普通文章URL
            # URL格式: https://dmscdj.lofter.com/post/84bc89fd_2be5a7548?incantation=rzD5Hc6Y0MGF
            url_pattern = r'https://([^.]+)\.lofter\.com/post/([^_]+)_([^?]+)'
            match = re.match(url_pattern, article_url)

            if not match:
                print("❌ 无法解析文章链接格式")
                return None

            blog_name = match.group(1)
            blog_id_hex = match.group(2)
            post_id_hex = match.group(3)

            print(f"📝 博客名: {blog_name}")
            print(f"🆔 博客ID (hex): {blog_id_hex}")
            print(f"📄 文章ID (hex): {post_id_hex}")

            # 将16进制ID转换为10进制
            try:
                blog_id = str(int(blog_id_hex, 16))
                post_id = str(int(post_id_hex, 16))
                print(f"🆔 博客ID (dec): {blog_id}")
                print(f"📄 文章ID (dec): {post_id}")
            except ValueError:
                print("❌ ID转换失败")
                return None

            # 方案1：先尝试直接API获取
            print("\n📡 方案1: 尝试直接API获取文章...")
            direct_result = self._try_direct_api(blog_id, post_id, article_url)
            if direct_result:
                return direct_result

            # 方案2：通过作者搜索获取（遵循JSON逻辑）
            print("\n🔍 方案2: 通过作者搜索获取文章（遵循JSON逻辑）...")

            # 获取博客信息
            blog_info = self._get_blog_info_by_name(blog_name, blog_id)
            if not blog_info:
                return None

            author_name = blog_info.get('blogNickName', '未知作者')
            print(f"👤 找到作者: {author_name}")

            # 获取作者的全部文章
            print(f"📚 获取作者 {author_name} 的全部文章...")
            posts = self.get_all_author_posts(blog_info)
            if not posts:
                print("❌ 无法获取作者文章列表")
                return None

            # 查找目标文章
            print(f"🔍 在 {len(posts)} 篇文章中查找目标文章...")
            target_post = None

            # 方法1：通过ID查找
            for post in posts:
                if post.get('post') and isinstance(post.get('post'), dict):
                    if str(post['post'].get('id', '')) == post_id:
                        target_post = post
                        print(f"✅ 通过ID找到目标文章")
                        break

            # 方法2：通过标题匹配
            if not target_post:
                print("💡 ID匹配失败，尝试标题匹配...")
                keywords = ['小鲛人', '王爷', '男风馆', '残缺', '尾巴', '讨好']

                best_match = None
                best_score = 0

                for post in posts:
                    # 提取标题
                    title = None
                    if post.get('post') and isinstance(post.get('post'), dict):
                        nested_post = post.get('post')
                        title = nested_post.get('title') or nested_post.get('digest')

                    if title:
                        title = self.clean_html(title).strip()

                        # 检查关键词匹配
                        matched_keywords = [keyword for keyword in keywords if keyword in title]
                        match_score = len(matched_keywords)

                        if match_score >= 3:  # 至少匹配3个关键词
                            if match_score > best_score:
                                best_score = match_score
                                best_match = post
                                print(f"🎯 找到匹配文章: {title}")
                                print(f"   匹配关键词: {matched_keywords} (得分: {match_score})")

                if best_match:
                    target_post = best_match
                    print(f"✅ 选择最佳匹配文章")

            if target_post:
                print(f"📖 开始下载文章内容...")
                return self.get_post_content(target_post)
            else:
                print("❌ 未找到目标文章")
                return None

        except Exception as e:
            print(f"❌ 智能下载失败: {e}")
            return None

    def _try_direct_api(self, blog_id: str, post_id: str, article_url: str) -> Optional[str]:
        """尝试直接API获取文章"""
        try:
            api_url = "https://api.lofter.com/oldapi/post/detail.api?product=lofter-android-7.4.4"
            post_data = f"blogdomain=_blogid_{blog_id}.lofter.com&postid={post_id}"

            response = self.session.post(api_url, data=post_data, headers=self.headers)

            if response.status_code != 200:
                print(f"📊 直接API响应状态码: {response.status_code}")
                return None

            data = response.json()

            if 'response' in data and 'post' in data['response']:
                post_data = data['response']['post']
                return self._extract_article_content(post_data, article_url)
            else:
                print("📋 直接API未返回文章数据")
                return None

        except Exception as e:
            print(f"📋 直接API获取失败: {e}")
            return None




    def _get_blog_info_by_name(self, blog_name: str, blog_id: str) -> Optional[Dict]:
        """通过博客名获取博客信息"""
        try:
            # 构造博客信息（模拟搜索结果）
            blog_info = {
                'blogName': blog_name,
                'blogId': blog_id,
                'blogNickName': '未知作者'  # 这个会在获取文章列表时更新
            }

            # 尝试通过API获取更详细的博客信息
            api_url = "http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-7.4.4"
            post_data = {
                "targetblogid": blog_id,
                "method": "getBlogInfoDetail",
                "returnData": "1",
                "checkpwd": "1",
                "needgetpoststat": "1"
            }

            response = self.session.post(api_url, data=post_data, headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                if 'response' in data and 'blogInfo' in data['response']:
                    blog_detail = data['response']['blogInfo']
                    blog_info['blogNickName'] = blog_detail.get('blogNickName', '未知作者')
                    print(f"✅ 获取到博客详细信息: {blog_info['blogNickName']}")

            return blog_info

        except Exception as e:
            print(f"❌ 获取博客信息失败: {e}")
            return None

    def _find_post_by_title_old(self, posts: List[Dict]) -> Optional[Dict]:
        """通过标题匹配查找文章"""
        try:
            print("\n📚 显示文章列表进行标题匹配:")
            print("=" * 60)

            # 调试：显示第一篇文章的完整数据结构
            if posts:
                print("🔍 调试信息 - 第一篇文章的数据结构:")
                first_post = posts[0]
                print(f"   可用字段: {list(first_post.keys())}")
                if 'post' in first_post:
                    nested_post = first_post['post']
                    print(f"   嵌套post字段: {list(nested_post.keys()) if isinstance(nested_post, dict) else type(nested_post)}")
                print()

            valid_posts = []
            for i, post in enumerate(posts, 1):
                # 尝试多种方式获取标题，包括嵌套的post对象
                title = None

                # 方式1：直接从post对象获取
                if post.get('title'):
                    title = post.get('title')
                elif post.get('digest'):
                    title = post.get('digest')
                elif post.get('noticeLinkTitle'):
                    title = post.get('noticeLinkTitle')

                # 方式2：从嵌套的post对象获取
                elif post.get('post'):
                    nested_post = post.get('post')
                    if nested_post.get('title'):
                        title = nested_post.get('title')
                    elif nested_post.get('digest'):
                        title = nested_post.get('digest')

                # 清理HTML标签
                if title:
                    title = self.clean_html(title).strip()

                # 如果仍然没有有效标题，使用默认标题
                if not title or title.isspace():
                    title = f'文章{i}'

                valid_posts.append((i, post, title))
                # 只显示前20篇文章，避免输出过多
                if len(valid_posts) <= 20:
                    print(f"{len(valid_posts):3d}. {title}")

            if len(valid_posts) > 20:
                print(f"... 还有 {len(valid_posts) - 20} 篇文章")

            if not valid_posts:
                print("❌ 没有找到有效的文章")
                return None

            print(f"\n找到 {len(valid_posts)} 篇有效文章")
            print("🎯 目标文章关键词:")
            print("   - 小鲛人")
            print("   - 王爷")
            print("   - 男风馆")
            print("   - 残缺的尾巴")
            print("   - 讨好所有人")

            # 自动搜索匹配的文章
            keywords = ['小鲛人', '王爷', '男风馆', '残缺', '尾巴', '讨好']

            print(f"\n🔍 自动搜索包含关键词的文章...")
            best_match = None
            best_score = 0

            # 显示所有匹配的文章
            matches = []
            for _, post, title in valid_posts:
                # 检查标题是否包含关键词
                matched_keywords = [keyword for keyword in keywords if keyword in title]
                match_count = len(matched_keywords)

                if match_count > 0:
                    matches.append((post, title, match_count, matched_keywords))
                    print(f"🎯 找到匹配: {title}")
                    print(f"   匹配关键词: {matched_keywords} (共{match_count}个)")

                if match_count > best_score:
                    best_score = match_count
                    best_match = (post, title)

                # 如果找到包含多个关键词的文章，直接返回
                if match_count >= 2:
                    print(f"✅ 自动选择最佳匹配: {title}")
                    return post

            if not matches:
                print("❌ 没有找到包含目标关键词的文章")
                print("💡 可能的原因:")
                print("   - 目标文章不在当前获取的文章列表中")
                print("   - 文章标题与预期关键词不匹配")
                print("   - 需要获取更多文章（尝试完整获取）")

            # 如果有部分匹配，询问用户
            if best_match and best_score > 0:
                post, title = best_match
                print(f"🤔 找到可能匹配的文章: {title}")
                print(f"   匹配关键词数: {best_score}")
                confirm = input("是否选择这篇文章? (y/n): ").strip().lower()
                if confirm == 'y':
                    return post

            # 让用户手动选择
            print("❌ 自动搜索未找到明确匹配的文章")
            print("💡 请手动选择文章:")

            try:
                choice = input(f"请输入文章序号 (1-{len(valid_posts)}): ").strip()
                if choice.isdigit():
                    choice_idx = int(choice) - 1
                    if 0 <= choice_idx < len(valid_posts):
                        _, post, title = valid_posts[choice_idx]
                        print(f"✅ 选择文章: {title}")
                        return post
                    else:
                        print("❌ 序号超出范围")
                        return None
                else:
                    print("❌ 请输入有效数字")
                    return None
            except KeyboardInterrupt:
                print("\n❌ 用户取消操作")
                return None

        except Exception as e:
            print(f"❌ 标题匹配失败: {e}")
            return None

    def _extract_article_content(self, post_data: Dict, article_url: str) -> str:
        """提取文章内容"""
        content = post_data.get('content', '')
        title = post_data.get('title', post_data.get('digest', '无标题'))
        author = post_data.get('blogInfo', {}).get('blogNickName', '未知作者')
        publish_time = post_data.get('publishTime', '')

        if publish_time:
            try:
                import datetime
                publish_time = datetime.datetime.fromtimestamp(int(publish_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass

        # 清理HTML标签
        content = self.clean_html(content)

        if not content.strip():
            print("⚠️ 文章内容为空，可能需要特殊权限访问")
            return None

        result = f"标题: {title}\n作者: {author}\n"
        if publish_time:
            result += f"发布时间: {publish_time}\n"
        result += f"原文链接: {article_url}\n"
        result += f"\n{'-'*50}\n\n{content}"

        print(f"✅ 成功获取文章内容 ({len(content)} 字符)")
        return result

    def extract_post_content(self, post_data: Dict) -> Optional[str]:
        """提取文章内容（用于合集文章）"""
        try:
            if not post_data:
                return None

            # 处理嵌套的post结构
            if 'post' in post_data and isinstance(post_data['post'], dict):
                actual_post = post_data['post']
            else:
                actual_post = post_data

            # 提取基本信息
            title = actual_post.get('title', actual_post.get('digest', '无标题'))
            content = actual_post.get('content', '')
            author_info = actual_post.get('blogInfo', {})
            author = author_info.get('blogNickName', '未知作者')
            publish_time = actual_post.get('publishTime', '')

            # 如果没有content字段，尝试其他字段
            if not content:
                content = actual_post.get('digest', '')

            if not content:
                print(f"⚠️ 文章 '{title}' 没有可提取的内容")
                return None

            # 清理HTML标签
            content = self.clean_html(content)

            if not content.strip():
                print(f"⚠️ 文章 '{title}' 内容为空")
                return None

            # 构建完整内容
            result = f"标题: {title}\n作者: {author}\n"
            if publish_time:
                result += f"发布时间: {publish_time}\n"
            result += f"\n{'-'*50}\n\n{content}"

            return result

        except Exception as e:
            print(f"❌ 提取文章内容失败: {e}")
            return None

    def clean_html(self, text: str) -> str:
        """清理HTML标签和转义字符"""
        if not text:
            return ""
        
        # 解码HTML实体
        text = html.unescape(text)
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 清理多余的空白字符
        text = re.sub(r'\n\s*\n', '\n\n', text)
        text = text.strip()
        
        return text
    
    def save_article(self, title: str, content: str, author: str, output_dir: str = "downloads") -> bool:
        """保存文章到文件"""
        try:
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 清理文件名中的非法字符，包括中文括号和特殊符号
            safe_title = re.sub(r'[<>:"/\\|?*（）()【】\[\]{}]', '_', title)
            safe_author = re.sub(r'[<>:"/\\|?*（）()【】\[\]{}]', '_', author)

            # 限制文件名长度
            if len(safe_title) > 100:
                safe_title = safe_title[:100] + "..."
            if len(safe_author) > 50:
                safe_author = safe_author[:50] + "..."

            filename = f"{safe_author}_{safe_title}.txt"
            filepath = os.path.join(output_dir, filename)

            # 如果文件已存在，添加序号
            counter = 1
            original_filepath = filepath
            while os.path.exists(filepath):
                name, ext = os.path.splitext(original_filepath)
                filepath = f"{name}_{counter}{ext}"
                counter += 1

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"作者: {author}\n")
                f.write(f"标题: {title}\n")
                f.write("=" * 50 + "\n\n")
                f.write(content)

            print(f"✅ 文章已保存: {filepath}")
            return True

        except Exception as e:
            print(f"❌ 保存文章失败: {e}")
            print(f"💡 标题: {title}")
            print(f"💡 作者: {author}")
            return False


def main():
    """主程序"""
    downloader = LofterDownloader()

    print("=" * 60)
    print("🌟 Lofter文章下载器 - 定制版")
    print("📚 内置Cookie，即开即用")
    print("=" * 60)

    # 内置Cookie已自动加载
    if not downloader.cookies:
        print("❌ 内置Cookie加载失败")
        print("💡 请检查代码中的Cookie配置是否正确")
        return

    print("✅ Cookie加载成功，可以开始使用")
    
    while True:
        print("\n" + "=" * 50)
        print("📋 主菜单")
        print("=" * 50)
        print("1. 🔍 搜索作者")
        print("2. 📚 搜索合集 (#合集)")
        print("3. 🔗 通过链接下载文章")
        print("4. 📖 测试功能")
        print("5. ❓ 帮助信息")
        print("6. 🚪 退出程序")

        choice = input("\n请选择操作 (1-6): ").strip()

        if choice == '1':
            # 搜索作者功能
            print("\n" + "🔍 作者搜索")
            print("=" * 30)
            print("💡 提示: 可以搜索作者的昵称或用户名")
            print("📝 示例: 村口做饭大厨")

            author_name = input("\n请输入作者名: ").strip()
            if not author_name:
                print("❌ 作者名不能为空")
                continue

            # 搜索作者
            search_result = downloader.search_author(author_name)
            if not search_result:
                print("\n💡 搜索建议:")
                print("- 检查作者名是否正确")
                print("- 尝试使用作者的完整昵称")
                print("- 确保网络连接正常")
                continue

            # 显示搜索结果
            blogs = search_result.get('blogs', [])
            if not blogs:
                print("❌ 未找到匹配的作者")
                continue

            print(f"\n✅ 找到 {len(blogs)} 个匹配的作者:")
            print("-" * 40)
            for i, blog in enumerate(blogs, 1):
                nick_name = blog.get('blogNickName', '未知')
                blog_name = blog.get('blogName', '未知')
                intro = blog.get('selfIntro', '无简介')[:30]
                print(f"{i}. {nick_name}")
                print(f"   用户名: @{blog_name}")
                print(f"   简介: {intro}...")
                print()

            try:
                blog_choice = input("请选择作者 (输入序号): ").strip()
                if not blog_choice.isdigit():
                    print("❌ 请输入有效的数字")
                    continue

                blog_index = int(blog_choice) - 1
                if 0 <= blog_index < len(blogs):
                    selected_blog = blogs[blog_index]

                    print(f"\n📚 正在获取 {selected_blog.get('blogNickName')} 的文章列表...")
                    print("请选择获取方式:")
                    print("1. 快速获取（最多500篇文章）")
                    print("2. 完整获取（所有文章，可能需要较长时间）")

                    get_choice = input("请选择 (1-2): ").strip()

                    if get_choice == '2':
                        # 获取全部文章
                        posts = downloader.get_all_author_posts(selected_blog)
                    else:
                        # 获取文章列表（默认最多500篇）
                        posts = downloader.get_author_posts(selected_blog)
                    if not posts:
                        print("💡 可能的原因:")
                        print("- 该作者没有公开文章")
                        print("- 需要特殊权限访问")
                        print("- 网络连接问题")
                        continue

                    # 显示文章列表
                    print(f"\n� {selected_blog.get('blogNickName')} 的文章列表 (共{len(posts)}篇):")
                    print("=" * 60)



                    for i, post in enumerate(posts, 1):
                        # 尝试多种方式获取标题
                        title = (post.get('title') or
                                post.get('digest') or
                                post.get('noticeLinkTitle') or
                                post.get('post', {}).get('title') or
                                post.get('post', {}).get('digest') or
                                f'文章{i}')

                        # 清理HTML标签
                        if title:
                            title = downloader.clean_html(title).strip()

                        # 如果标题为空或只有空白字符，使用默认标题
                        if not title or title.isspace():
                            title = f'文章{i}'

                        # 显示完整标题，不截断
                        print(f"{i:3d}. {title}")

                    print(f"\n{len(posts) + 1:3d}. 📦 下载全部文章")
                    print(f"{len(posts) + 2:3d}. 🔙 返回主菜单")

                    while True:
                        post_choice = input(f"\n请选择要下载的文章 (1-{len(posts)+2}): ").strip()

                        if post_choice == str(len(posts) + 2):
                            # 返回主菜单
                            break
                        elif post_choice == str(len(posts) + 1) or post_choice.lower() == 'all':
                            # 下载全部文章
                            print(f"\n📦 开始批量下载 {len(posts)} 篇文章...")
                            print("⏳ 请耐心等待，避免请求过于频繁...")

                            success_count = 0
                            for i, post in enumerate(posts, 1):
                                title = post.get('title', post.get('digest', f'文章{i}'))
                                print(f"\n[{i}/{len(posts)}] 下载: {title[:30]}...")

                                content = downloader.get_post_content(post)
                                if content:
                                    downloader.save_article(title, content, selected_blog.get('blogNickName', '未知作者'))
                                    success_count += 1
                                    print(f"✅ 成功")
                                else:
                                    print(f"❌ 失败")

                                # 避免请求过快
                                if i < len(posts):
                                    time.sleep(2)

                            print(f"\n🎉 批量下载完成!")
                            print(f"📊 成功: {success_count}/{len(posts)} 篇文章")
                            break
                        else:
                            try:
                                post_index = int(post_choice) - 1
                                if 0 <= post_index < len(posts):
                                    selected_post = posts[post_index]
                                    title = selected_post.get('title', selected_post.get('digest', '无标题'))

                                    print(f"\n📖 下载文章: {title}")
                                    print("⏳ 正在获取文章内容...")

                                    content = downloader.get_post_content(selected_post)
                                    if content:
                                        downloader.save_article(title, content, selected_blog.get('blogNickName', '未知作者'))
                                        print("✅ 文章下载完成!")

                                        # 询问是否继续下载
                                        continue_choice = input("\n是否继续下载其他文章? (y/n): ").strip().lower()
                                        if continue_choice != 'y':
                                            break
                                    else:
                                        print("❌ 文章下载失败")
                                else:
                                    print("❌ 无效的文章序号")
                            except ValueError:
                                print("❌ 请输入有效的数字")
                else:
                    print("❌ 无效的作者序号")
            except ValueError:
                print("❌ 请输入有效的数字")

        elif choice == '2':
            # 搜索合集功能
            print("\n📚 搜索合集")
            print("=" * 30)
            print("💡 提示: 搜索包含 '#合集' 标签的文章")
            print("📝 默认搜索关键词: #合集")

            keyword = input("\n请输入搜索关键词 (直接回车使用默认 '#合集'): ").strip()
            if not keyword:
                keyword = "#合集"

            print(f"\n🔍 搜索关键词: {keyword}")

            # 搜索合集
            search_result = downloader.search_collections(keyword)
            if not search_result:
                print("\n💡 搜索建议:")
                print("- 尝试使用其他关键词")
                print("- 确保网络连接正常")
                print("- 检查Cookie是否有效")
                continue

            # 处理合集文章
            collection_posts = downloader.get_collection_posts(search_result)
            if not collection_posts:
                print("❌ 没有找到合集文章")
                continue

            # 检查是合集列表还是文章列表
            if collection_posts and 'name' in collection_posts[0]:
                # 这是合集列表
                print(f"\n✅ 找到 {len(collection_posts)} 个合集:")
                print("-" * 50)

                for i, collection in enumerate(collection_posts, 1):
                    name = collection.get('name', '未知合集')
                    author = collection.get('blogNickName', '未知作者')
                    post_count = collection.get('postCount', 0)

                    # 限制名称长度
                    if len(name) > 50:
                        name = name[:50] + "..."

                    print(f"{i:3d}. {name}")
                    print(f"     👤 创建者: {author}")
                    print(f"     📊 文章数: {post_count}")
                    print()
            else:
                # 这是文章列表
                print(f"\n✅ 找到 {len(collection_posts)} 个合集文章:")
                print("-" * 50)

                for i, post in enumerate(collection_posts, 1):
                    if 'post' in post and isinstance(post['post'], dict):
                        post_data = post['post']
                        title = post_data.get('title', post_data.get('digest', '无标题'))
                        author = post_data.get('blogInfo', {}).get('blogNickName', '未知作者')

                        # 限制标题长度
                        if len(title) > 50:
                            title = title[:50] + "..."

                        print(f"{i:3d}. {title}")
                        print(f"     👤 作者: {author}")
                        print()

            # 根据内容类型显示不同的选项
            if collection_posts and 'name' in collection_posts[0]:
                print(f"{len(collection_posts) + 1:3d}. 📦 下载全部合集")
            else:
                print(f"{len(collection_posts) + 1:3d}. 📦 下载全部合集文章")
            print(f"{len(collection_posts) + 2:3d}. 🔙 返回主菜单")

            while True:
                collection_choice = input(f"\n请选择要下载的合集文章 (1-{len(collection_posts)+2}): ").strip()

                if collection_choice == str(len(collection_posts) + 2):
                    # 返回主菜单
                    break
                elif collection_choice == str(len(collection_posts) + 1):
                    # 下载全部合集文章
                    print(f"\n🚀 开始下载全部 {len(collection_posts)} 个合集...")

                    # 检查是否是合集信息还是文章列表
                    if collection_posts and 'name' in collection_posts[0]:
                        # 这是合集信息，需要先获取合集中的文章
                        success_count = 0
                        for collection in collection_posts:
                            collection_id = str(collection.get('id', ''))
                            collection_name = collection.get('name', '未知合集')

                            print(f"\n📚 处理合集: {collection_name}")

                            # 获取合集中的文章
                            posts = downloader._get_collection_posts_by_id(collection_id, collection)

                            if posts:
                                print(f"✅ 获取到 {len(posts)} 篇文章")

                                # 下载合集中的文章
                                if downloader.download_collection_articles(posts):
                                    success_count += 1
                                    print(f"✅ 合集 '{collection_name}' 下载完成!")
                                else:
                                    print(f"❌ 合集 '{collection_name}' 下载失败")
                            else:
                                print(f"❌ 无法获取合集 '{collection_name}' 的文章")

                        if success_count > 0:
                            print(f"\n🎉 成功下载 {success_count}/{len(collection_posts)} 个合集!")
                        else:
                            print("\n❌ 所有合集下载失败")
                    else:
                        # 这是文章列表，直接下载
                        if downloader.download_collection_articles(collection_posts):
                            print("🎉 全部合集文章下载完成!")
                        else:
                            print("❌ 合集文章下载失败")
                    break
                else:
                    try:
                        choice_idx = int(collection_choice) - 1
                        if 0 <= choice_idx < len(collection_posts):
                            selected_item = collection_posts[choice_idx]

                            # 检查是合集信息还是文章信息
                            if 'name' in selected_item:
                                # 这是合集信息
                                collection_id = str(selected_item.get('id', ''))
                                collection_name = selected_item.get('name', '未知合集')
                                author = selected_item.get('blogNickName', '未知作者')

                                print(f"\n📚 选择的合集: {collection_name}")
                                print(f"👤 创建者: {author}")

                                # 获取合集中的文章
                                posts = downloader._get_collection_posts_by_id(collection_id, selected_item)

                                if posts:
                                    print(f"✅ 获取到 {len(posts)} 篇文章")

                                    # 下载合集中的文章
                                    if downloader.download_collection_articles(posts):
                                        print("✅ 合集下载完成!")
                                    else:
                                        print("❌ 合集下载失败")
                                else:
                                    print("❌ 无法获取合集文章")

                            elif 'post' in selected_item and isinstance(selected_item['post'], dict):
                                # 这是文章信息
                                post_data = selected_item['post']
                                title = post_data.get('title', post_data.get('digest', '无标题'))
                                author = post_data.get('blogInfo', {}).get('blogNickName', '未知作者')

                                print(f"\n📖 下载文章: {title}")
                                print(f"👤 作者: {author}")

                                # 提取并保存文章内容
                                content = downloader.extract_post_content(post_data)
                                if content:
                                    if downloader.save_article(title, content, author):
                                        print("✅ 文章下载完成!")
                                    else:
                                        print("❌ 文章保存失败")
                                else:
                                    print("❌ 无法提取文章内容")
                            else:
                                print("❌ 数据格式错误")
                                print(f"💡 可用字段: {list(selected_item.keys())}")

                            # 询问是否继续下载
                            continue_choice = input("\n是否继续下载其他合集? (y/n): ").strip().lower()
                            if continue_choice != 'y':
                                break
                        else:
                            print("❌ 无效的序号")
                    except ValueError:
                        print("❌ 请输入有效的数字")

        elif choice == '3':
            # 通过链接下载文章
            print("\n🔗 通过链接下载文章/合集")
            print("=" * 40)
            print("💡 支持的链接格式:")
            print("📄 文章链接:")
            print("   https://用户名.lofter.com/post/博客ID_文章ID")
            print("📚 合集链接:")
            print("   https://www.lofter.com/front/blog/collection/share?collectionId=合集ID")
            print("📝 示例:")
            print("   文章: https://dmscdj.lofter.com/post/84bc89fd_2be5a7548?incantation=rzD5Hc6Y0MGF")
            print("   合集: https://www.lofter.com/front/blog/collection/share?collectionId=23147679&incantation=hjbly2PvNNmM")

            article_url = input("\n请输入文章或合集链接: ").strip()
            if not article_url:
                print("❌ 链接不能为空")
                continue

            # 检查链接格式
            is_article_link = '.lofter.com/post/' in article_url
            is_collection_link = 'lofter.com/front/blog/collection/share' in article_url

            if not article_url.startswith('https://') or (not is_article_link and not is_collection_link):
                print("❌ 链接格式不正确")
                print("💡 支持的链接格式:")
                print("   - 文章链接: https://用户名.lofter.com/post/...")
                print("   - 合集链接: https://www.lofter.com/front/blog/collection/share?...")
                continue

            print(f"\n🔗 开始下载文章...")
            content = downloader.download_from_url(article_url)

            if content:
                # 从内容中提取标题和作者
                lines = content.split('\n')
                title = "未知标题"
                author = "未知作者"

                for line in lines:
                    if line.startswith('标题: '):
                        title = line[3:].strip()
                    elif line.startswith('作者: '):
                        author = line[3:].strip()

                # 保存文章
                downloader.save_article(title, content, author)
                print("✅ 文章下载并保存成功!")
            else:
                print("❌ 文章下载失败")
                print("💡 可能的原因:")
                print("- 链接格式不正确")
                print("- 文章需要特殊权限访问")
                print("- Cookie认证失败")
                print("- 文章不存在或已被删除")

        elif choice == '4':
            # 测试功能
            print("\n🧪 功能测试")
            print("=" * 30)
            print("这将运行测试脚本来验证各个功能模块")

            test_choice = input("是否运行测试? (y/n): ").strip().lower()
            if test_choice == 'y':
                try:
                    import test_downloader
                    test_downloader.main()
                except ImportError:
                    print("❌ 测试脚本不存在")
                except Exception as e:
                    print(f"❌ 测试运行失败: {e}")

        elif choice == '5':
            # 帮助信息
            print("\n❓ 帮助信息")
            print("=" * 40)
            print("📖 使用说明:")
            print("1. 确保已正确设置Cookie认证")
            print("2. 使用'搜索作者'功能查找目标作者")
            print("3. 使用'搜索合集'功能查找包含 '#合集' 标签的文章")
            print("4. 从搜索结果中选择正确的作者或合集")
            print("5. 浏览文章列表并选择下载")
            print("6. 或者直接使用'通过链接下载文章'功能")
            print()
            print("📚 合集下载说明:")
            print("- 搜索包含 '#合集' 标签的文章")
            print("- 支持批量下载所有合集文章")
            print("- 可以单独选择下载特定合集文章")
            print()
            print("🔗 链接下载说明:")
            print("- 支持文章链接和合集链接下载")
            print("- 文章链接格式: https://用户名.lofter.com/post/...")
            print("- 合集链接格式: https://www.lofter.com/front/blog/collection/share?...")
            print("- 文章示例: https://dmscdj.lofter.com/post/84bc89fd_2be5a7548")
            print("- 合集示例: https://www.lofter.com/front/blog/collection/share?collectionId=23147679")
            print("- 合集支持三种下载模式: 单文件合并/分别下载/仅查看信息")
            print()
            print("🔧 故障排除:")
            print("- 认证失败: 重新获取Cookie")
            print("- 搜索无结果: 检查关键词拼写")
            print("- 下载失败: 检查网络连接")
            print("- 链接无效: 检查链接格式是否正确")
            print()
            print("📁 文件说明:")
            print("- cookies.txt: 存储登录认证信息")
            print("- downloads/: 下载的文章保存目录")
            print("- test_downloader.py: 功能测试脚本")

        elif choice == '6':
            print("\n👋 感谢使用Lofter文章下载器!")
            print("🌟 如有问题，请检查帮助信息或重新设置Cookie")
            break
        else:
            print("❌ 无效的选择，请输入1-6之间的数字")


if __name__ == "__main__":
    main()
