#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lofter文章下载QQ机器人插件
直接在代码中配置Cookie，无需外部文件
"""

import html
import os
import tempfile
import httpx
from nonebot import on_message, on_regex
from nonebot.adapters.onebot.v11 import Event, Bot, GroupMessageEvent, Message, MessageSegment
from nonebot import logger
from nonebot.matcher import Matcher
from contextlib import asynccontextmanager
import re
import time
import asyncio
from bs4 import BeautifulSoup

# ==================== 配置区域 ====================
# 请在这里填入您的Lofter Cookie
LOFTER_COOKIE_KEY = "LOFTER-PHONE-LOGIN-AUTH"
LOFTER_COOKIE_VALUE = "JxNkxwh5V60_c2ha0_61GMAdR9FTtoGpOaOMvp3akrxrfTq1uLuDhIO47vTZEqmHSQ21n_2u70k1QUdfXgS9SFDB_zB32lo-"

# WebDAV配置（可选）
WEBDAV_URL = "https://al.zyii.xyz:666/dav"
WEBDAV_USERNAME = "yuedu"
WEBDAV_PASSWORD = "123456"
WEBDAV_FOLDER = "琪露诺上传的《小说》"

# 下载方法设置
ENABLE_HTML_PARSING = True  # 是否启用HTML解析作为备选下载方法
ENABLE_KEYWORD_MATCHING = False  # 是否启用关键词匹配作为备选方案
ARTICLE_KEYWORDS = []  # 关键词列表（仅在启用关键词匹配时使用）

# 说明：
# - 优先使用API方法通过文章ID直接查找文章
# - 如果API方法失败且启用了HTML解析，会尝试直接解析网页内容
# - 如果ID查找失败且启用了关键词匹配，会尝试通过关键词查找
# - 如果都失败，会下载作者的第一篇文章
# ==================== 配置区域结束 ====================

# 导入插件管理器
try:
    from ..plugin_manager import PluginPriority, log_plugin_info, conditional_block
    from nonebot.rule import Rule
except ImportError:
    # 如果没有插件管理器，使用默认值
    class PluginPriority:
        HIGH = 5
        CRITICAL = 1
        ULTRA_HIGH = 0  # 最高优先级

    def log_plugin_info(name, desc, priority, block):
        logger.info(f"插件: {name} - {desc}")

    async def conditional_block(matcher, block):
        pass

    from nonebot.rule import Rule

# 存储下载状态
download_status = {}

# 创建Lofter链接匹配规则
def lofter_url_rule() -> Rule:
    async def _rule(event: Event) -> bool:
        if not isinstance(event, GroupMessageEvent):
            return False
        raw_message = str(event.get_message()).strip()

        # 匹配Lofter链接（包括文章链接和合集链接）
        article_pattern = r"https?://[\w-]+\.lofter\.com/post/[a-f0-9]+_[a-f0-9]+"
        collection_pattern = r"https?://www\.lofter\.com/front/blog/collection/share\?collectionId=\d+"

        return bool(re.search(article_pattern, raw_message) or re.search(collection_pattern, raw_message))
    return Rule(_rule)

# 创建匹配器 - 使用最高优先级确保能拦截Lofter链接
download_lofter = on_regex(
    r"https?://(?:[\w-]+\.lofter\.com/post/[a-f0-9]+_[a-f0-9]+|www\.lofter\.com/front/blog/collection/share\?collectionId=\d+)",
    priority=1,  # 使用最高优先级
    block=True,
    rule=lofter_url_rule()
)

progress_query = on_message(priority=1)  # 同样使用最高优先级

# 记录插件信息
log_plugin_info("Lofter文章下载", "Lofter文章链接下载", PluginPriority.HIGH, True)
log_plugin_info("Lofter下载进度", "下载进度查询", PluginPriority.CRITICAL, False)

@asynccontextmanager
async def async_tempfile():
    """异步临时文件管理器"""
    temp = tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.txt')
    try:
        yield temp
    finally:
        temp.close()

@progress_query.handle()
async def handle_progress(matcher: Matcher, bot: Bot, event: Event):
    """处理进度查询"""
    if not isinstance(event, GroupMessageEvent):
        return
        
    raw_message = str(event.get_message()).strip()
    cleaned_message = html.unescape(raw_message)
    
    if "Lofter下载进度" in cleaned_message or "lofter下载进度" in cleaned_message:
        await conditional_block(matcher, True)
        
        user_progress = download_status.get(event.user_id, [])
        progress_text = "Lofter下载进度：\n"
        if not user_progress:
            progress_text += "没有进行中的下载啦\n有想看的Lofter文章喵？快告诉我"
        else:
            for i, article_progress in enumerate(user_progress, 1):
                progress_text += f"{i}. {article_progress['progress']}\n"
        
        await bot.send(event, progress_text)
        await progress_query.finish()

@download_lofter.handle()
async def handle_download_lofter(bot: Bot, event: Event):
    """处理Lofter链接下载"""
    try:
        if not isinstance(event, GroupMessageEvent):
            return
            
        raw_message = str(event.get_message()).strip()
        logger.info(f"收到Lofter链接：{raw_message}")
        
        # 检查Cookie配置
        if not LOFTER_COOKIE_KEY or not LOFTER_COOKIE_VALUE:
            await bot.send(event, "❌ 未配置Lofter Cookie，请联系管理员配置")
            return
        
        cleaned_message = html.unescape(raw_message)
        await bot.send(event, "       稍候喵❤\n正在处理您的Lofter文章请求啦")

        # 提取链接
        lofter_url = extract_lofter_url(cleaned_message)
        if not lofter_url:
            await bot.send(event, "❌ 链接格式错误，请检查Lofter链接")
            return

        # 智能下载 - 检查是否为合集链接
        if 'lofter.com/front/blog/collection/share' in lofter_url:
            logger.info("📚 检测到合集链接，使用合集下载模式...")
            result = await download_lofter_collection(lofter_url)
        else:
            # 下载文章 - 先尝试API方法，失败后尝试HTML解析
            result = await download_lofter_article(lofter_url)
            if not result and ENABLE_HTML_PARSING:
                logger.info("🔄 API方法失败，尝试HTML解析方法...")
                result = await download_lofter_article_html(lofter_url)

        if not result:
            error_msg = "❌ 下载失败，请检查链接或稍后重试"
            if ENABLE_HTML_PARSING:
                error_msg = "❌ 下载失败，API和HTML解析方法都无法获取文章内容"
            await bot.send(event, error_msg)
            return

        title, content, author = result
        
        # 更新下载状态
        await update_download_status(event.user_id, title, 1)

        # 创建文件内容
        if 'lofter.com/front/blog/collection/share' in lofter_url:
            # 合集内容已经包含完整格式
            article_text = content
        else:
            # 单篇文章需要添加格式
            article_text = f"标题: {title}\n作者: {author}\n\n{'-'*50}\n\n{content}"
        
        # 使用临时文件
        async with async_tempfile() as tmp_file:
            tmp_file.write(article_text.encode('utf-8'))
            tmp_path = tmp_file.name

            try:
                # 读取文件
                with open(tmp_path, 'rb') as f:
                    file_content = f.read()
                
                # 安全文件名
                safe_name = re.sub(r'[\\/:"*?<>|]+', "_", f"{author}_{title}.txt")
                
                upload_success = False
                temp_path = None
                
                try:
                    # 创建临时文件用于上传
                    with tempfile.NamedTemporaryFile(delete=False, prefix=f"{event.message_id}_", suffix=".txt") as temp:
                        temp.write(file_content)
                        temp.flush()
                        os.fsync(temp.fileno())
                        temp_path = temp.name
                    
                    # 尝试上传到群文件
                    try:
                        folder_id = await get_folder_id(bot, event.group_id, "Lofter文章")
                        await bot.call_api(
                            "upload_group_file",
                            group_id=event.group_id,
                            file=temp_path,
                            name=safe_name,
                            folder=folder_id if folder_id else ""
                        )
                        
                        upload_success = True
                        logger.info("群文件上传成功")
                        
                        # 发送成功消息
                        success_msg = Message([
                            MessageSegment.at(event.user_id),
                            MessageSegment.text(f"\n 《{title}》\n作者：{author}\n------------\n  下载完毕了喵~\n已上传至群文件❤\n------------")
                        ])
                        await bot.send(event, success_msg)
                        
                    except Exception as e:
                        logger.error(f"群文件上传失败: {e}")
                
                except Exception as e:
                    logger.error(f"文件处理失败: {e}")
                
                # 如果群文件上传失败，尝试WebDAV
                if not upload_success:
                    logger.info("尝试上传到WebDAV...")
                    if not temp_path or not os.path.exists(temp_path):
                        with tempfile.NamedTemporaryFile(delete=False, prefix=f"{event.message_id}_", suffix=".txt") as temp:
                            temp.write(file_content)
                            temp.flush()
                            temp_path = temp.name
                    
                    download_url = await upload_to_webdav(temp_path, safe_name)
                    
                    if download_url:
                        web_msg = Message([
                            MessageSegment.at(event.user_id),
                            MessageSegment.text(f"\n 《{title}》\n作者：{author}\n------------\n  下载完毕了喵~\n请通过以下链接下载\n------------\n{download_url}")
                        ])
                        await bot.send(event, web_msg)
                    else:
                        error_msg = Message([
                            MessageSegment.at(event.user_id),
                            MessageSegment.text(f" \n 下载失败，请稍后重试喵～")
                        ])
                        await bot.send(event, error_msg)
                
                # 清理临时文件
                if temp_path and os.path.exists(temp_path):
                    os.remove(temp_path)
                
            except Exception as e:
                logger.error(f"文件处理错误: {e}")
                error_msg = Message([
                    MessageSegment.at(event.user_id),
                    MessageSegment.text(f" \n 文件处理失败，请稍后重试喵～")
                ])
                await bot.send(event, error_msg)
        
        # 清理下载状态
        clear_download_status(event.user_id, title)
        
    except Exception as e:
        logger.error(f"下载Lofter文章错误: {e}")
        error_msg = Message([
            MessageSegment.at(event.user_id),
            MessageSegment.text(f" \n 下载过程中发生错误，请稍后重试喵～")
        ])
        await bot.send(event, error_msg)

def extract_lofter_url(text: str) -> str:
    """提取Lofter链接（支持文章链接和合集链接）"""
    # 先处理HTML实体编码
    text = html.unescape(text)

    # 先尝试匹配文章链接
    article_pattern = r'https?://[\w-]+\.lofter\.com/post/[a-f0-9]+_[a-f0-9]+(?:\?[^\s]*)?'
    match = re.search(article_pattern, text)
    if match:
        return match.group(0)

    # 再尝试匹配合集链接
    collection_pattern = r'https?://www\.lofter\.com/front/blog/collection/share\?collectionId=\d+(?:&[^\s]*)?'
    match = re.search(collection_pattern, text)
    if match:
        return match.group(0)

    return None



async def download_lofter_article(url: str):
    """下载Lofter文章 - 使用与lofter_downloader.py相同的成功方法"""
    try:
        logger.info(f"🔗 智能下载链接: {url}")

        # 解析普通文章URL
        url_pattern = r'https://([^.]+)\.lofter\.com/post/([^_]+)_([^?]+)'
        match = re.match(url_pattern, url)

        if not match:
            logger.error("❌ 无法解析文章链接格式")
            return None

        blog_name = match.group(1)
        blog_id_hex = match.group(2)
        post_id_hex = match.group(3)

        logger.info(f"📝 博客名: {blog_name}")
        logger.info(f"🆔 博客ID (hex): {blog_id_hex}")
        logger.info(f"📄 文章ID (hex): {post_id_hex}")

        # 将16进制ID转换为10进制
        try:
            blog_id = str(int(blog_id_hex, 16))
            post_id = str(int(post_id_hex, 16))
            logger.info(f"🆔 博客ID (dec): {blog_id}")
            logger.info(f"📄 文章ID (dec): {post_id}")
        except ValueError:
            logger.error("❌ ID转换失败")
            return None

        # 设置请求头
        headers = {
            "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            LOFTER_COOKIE_KEY: LOFTER_COOKIE_VALUE
        }

        # 方案1：先尝试直接API获取
        logger.info("📡 方案1: 尝试直接API获取文章...")
        direct_result = await try_direct_api(blog_id, post_id, url, headers)
        if direct_result:
            return direct_result

        # 方案2：通过作者搜索获取
        logger.info("🔍 方案2: 通过作者搜索获取文章...")

        # 获取博客信息
        blog_info = await get_blog_info_by_name(blog_name, blog_id, headers)
        if not blog_info:
            return None

        author_name = blog_info.get('blogNickName', '未知作者')
        logger.info(f"👤 找到作者: {author_name}")

        # 获取作者的全部文章
        logger.info(f"📚 获取作者 {author_name} 的全部文章...")
        posts = await get_all_author_posts(blog_info, headers)
        if not posts:
            logger.error("❌ 无法获取作者文章列表")
            return None

        # 查找目标文章
        logger.info(f"🔍 在 {len(posts)} 篇文章中查找目标文章...")
        target_post = find_target_article_by_id(posts, post_id)

        if target_post:
            logger.info(f"📖 开始下载文章内容...")
            return await get_post_content_detailed(target_post, url, headers)
        else:
            logger.error("❌ 未找到目标文章")
            return None

    except Exception as e:
        logger.error(f"❌ 智能下载失败: {e}")
        return None

async def try_direct_api(blog_id: str, post_id: str, article_url: str, headers: dict):
    """尝试直接API获取文章"""
    try:
        api_url = "https://api.lofter.com/oldapi/post/detail.api?product=lofter-android-7.4.4"
        post_data = f"blogdomain=_blogid_{blog_id}.lofter.com&postid={post_id}"

        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.post(api_url, data=post_data, headers=headers)

            if response.status_code != 200:
                logger.info(f"📊 直接API响应状态码: {response.status_code}")
                return None

            data = response.json()

            if 'response' in data and 'post' in data['response']:
                post_data = data['response']['post']
                return extract_article_content_from_data(post_data, article_url)
            else:
                logger.info("📋 直接API未返回文章数据")
                return None

    except Exception as e:
        logger.info(f"📋 直接API获取失败: {e}")
        return None

async def get_blog_info_by_name(blog_name: str, blog_id: str, headers: dict):
    """通过博客名获取博客信息"""
    try:
        # 构造博客信息（模拟搜索结果）
        blog_info = {
            'blogName': blog_name,
            'blogId': blog_id,
            'blogNickName': '未知作者'  # 这个会在获取文章列表时更新
        }

        # 尝试通过API获取更详细的博客信息
        api_url = "http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-7.4.4"
        post_data = {
            "targetblogid": blog_id,
            "method": "getBlogInfoDetail",
            "returnData": "1",
            "checkpwd": "1",
            "needgetpoststat": "1"
        }

        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.post(api_url, data=post_data, headers=headers)
            if response.status_code == 200:
                data = response.json()
                if 'response' in data and 'blogInfo' in data['response']:
                    blog_detail = data['response']['blogInfo']
                    blog_info['blogNickName'] = blog_detail.get('blogNickName', '未知作者')
                    logger.info(f"✅ 获取到博客详细信息: {blog_info['blogNickName']}")

        return blog_info

    except Exception as e:
        logger.error(f"❌ 获取博客信息失败: {e}")
        return None

async def get_all_author_posts(blog_info: dict, headers: dict):
    """获取作者的所有文章列表（分页获取全部文章）"""
    try:
        blog_id = blog_info.get('blogId')
        blog_name = blog_info.get('blogName')

        if not blog_id or not blog_name:
            logger.error("❌ 缺少必要的博客信息")
            return None

        logger.info(f"📚 开始获取作者的全部文章...")
        logger.info(f"🆔 博客ID: {blog_id}")
        logger.info(f"📝 博客名: {blog_name}")

        all_posts = []
        offset = 0
        limit = 500
        page = 1

        async with httpx.AsyncClient(timeout=30) as client:
            while True:
                logger.info(f"📄 正在获取第 {page} 页文章 (偏移量: {offset})...")

                # 根据JSON配置构建获取文章列表的URL
                api_url = "http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-7.4.4"

                post_data = {
                    "targetblogid": blog_id,
                    "supportposttypes": "1,2,3,4,5,6",
                    "blogdomain": f"{blog_name}.lofter.com",
                    "offset": str(offset),
                    "method": "getPostLists",
                    "postdigestnew": "1",
                    "returnData": "1",
                    "limit": str(limit),
                    "checkpwd": "1",
                    "needgetpoststat": "1"
                }

                response = await client.post(api_url, data=post_data, headers=headers)

                if response.status_code == 401:
                    logger.error("❌ 认证失败，请检查Cookie是否有效")
                    return None
                elif response.status_code == 403:
                    logger.error("❌ 访问被拒绝，可能需要重新登录")
                    return None

                if response.status_code != 200:
                    break

                try:
                    data = response.json()
                except Exception as e:
                    logger.error(f"❌ 响应不是有效的JSON格式: {e}")
                    logger.error(f"响应内容: {response.text[:200]}...")
                    break

                # 检查响应数据结构
                if not isinstance(data, dict):
                    logger.error(f"❌ 响应数据不是字典格式: {type(data)}")
                    break

                if 'response' not in data:
                    logger.error("❌ 响应中没有 'response' 字段")
                    logger.error(f"可用字段: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")
                    break

                response_data = data['response']
                if not isinstance(response_data, dict):
                    logger.error(f"❌ response 字段不是字典格式: {type(response_data)}")
                    break

                if 'posts' not in response_data:
                    logger.error("❌ response 中没有 'posts' 字段")
                    logger.error(f"response 可用字段: {list(response_data.keys()) if isinstance(response_data, dict) else 'N/A'}")
                    break

                posts = response_data['posts']
                if posts is None:
                    logger.info(f"📄 第 {page} 页返回 None，没有更多文章")
                    break

                if not isinstance(posts, list):
                    logger.error(f"❌ posts 字段不是列表格式: {type(posts)}")
                    break

                if not posts:
                    # 没有更多文章了
                    logger.info(f"📄 第 {page} 页没有更多文章")
                    break

                all_posts.extend(posts)
                logger.info(f"✅ 第 {page} 页获取到 {len(posts)} 篇文章，累计 {len(all_posts)} 篇")

                # 如果这一页的文章数量少于limit，说明已经是最后一页
                if len(posts) < limit:
                    logger.info(f"📄 已获取完所有文章（最后一页只有 {len(posts)} 篇）")
                    break

                # 准备获取下一页
                offset += limit
                page += 1

        if all_posts:
            logger.info(f"🎉 总共获取到 {len(all_posts)} 篇文章")
            return all_posts
        else:
            logger.error("❌ 未获取到任何文章")
            return None

    except Exception as e:
        logger.error(f"❌ 获取文章列表失败: {e}")
        return None

def find_target_article_by_id(posts: list, post_id: str):
    """通过文章ID查找目标文章"""
    try:
        logger.info(f"查找文章ID: {post_id}")

        # 方法1: 通过文章ID精确匹配
        for post in posts:
            if post.get('post') and isinstance(post.get('post'), dict):
                nested_post = post.get('post')
                current_post_id = str(nested_post.get('id', ''))

                if current_post_id == post_id:
                    title = nested_post.get('title', nested_post.get('digest', '无标题'))
                    logger.info(f"✅ 通过ID找到文章: {title}")
                    return post

        # 如果没找到，返回第一篇文章作为默认选择
        if posts:
            first_post = posts[0]
            if first_post.get('post') and isinstance(first_post.get('post'), dict):
                title = first_post['post'].get('title', first_post['post'].get('digest', '无标题'))
                logger.warning(f"⚠️ 未找到精确匹配，返回第一篇文章: {title}")
                return first_post

        logger.error("❌ 未找到任何可用文章")
        return None

    except Exception as e:
        logger.error(f"❌ 查找文章失败: {e}")
        return None

async def get_post_content_detailed(post: dict, article_url: str, headers: dict):
    """获取单篇文章详细内容"""
    try:
        # 处理嵌套的post结构
        if 'post' in post and isinstance(post['post'], dict):
            actual_post = post['post']
            blog_id = actual_post.get('blogId')
            post_id = actual_post.get('id')
            title = actual_post.get('title', actual_post.get('digest', '无标题'))

            # 如果已经有完整的content，直接使用
            if actual_post.get('content'):
                content = actual_post.get('content')
                author = actual_post.get('blogInfo', {}).get('blogNickName', '未知作者')
                publish_time = actual_post.get('publishTime', '')

                if publish_time:
                    try:
                        import datetime
                        publish_time = datetime.datetime.fromtimestamp(int(publish_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass

                # 清理HTML标签
                content = clean_html_content(content)

                if not content.strip():
                    logger.warning("⚠️ 文章内容为空")
                    return None

                result = f"标题: {title}\n作者: {author}\n"
                if publish_time:
                    result += f"发布时间: {publish_time}\n"
                result += f"原文链接: {article_url}\n"
                result += f"\n{'-'*50}\n\n{content}"

                logger.info(f"✅ 成功获取文章内容 ({len(content)} 字符)")
                return title, result, author

        logger.error("❌ 无法提取文章内容")
        return None

    except Exception as e:
        logger.error(f"❌ 获取文章内容失败: {e}")
        return None

def extract_article_content_from_data(post_data: dict, article_url: str):
    """从文章数据中提取内容"""
    try:
        content = post_data.get('content', '')
        title = post_data.get('title', post_data.get('digest', '无标题'))
        author = post_data.get('blogInfo', {}).get('blogNickName', '未知作者')
        publish_time = post_data.get('publishTime', '')

        if publish_time:
            try:
                import datetime
                publish_time = datetime.datetime.fromtimestamp(int(publish_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass

        # 清理HTML标签
        content = clean_html_content(content)

        if not content.strip():
            logger.warning("⚠️ 文章内容为空，可能需要特殊权限访问")
            return None

        result = f"标题: {title}\n作者: {author}\n"
        if publish_time:
            result += f"发布时间: {publish_time}\n"
        result += f"原文链接: {article_url}\n"
        result += f"\n{'-'*50}\n\n{content}"

        logger.info(f"✅ 成功获取文章内容 ({len(content)} 字符)")
        return title, result, author

    except Exception as e:
        logger.error(f"❌ 提取文章内容失败: {e}")
        return None

async def download_lofter_article_html(url: str):
    """通过HTML解析下载Lofter文章"""
    try:
        logger.info(f"🌐 开始HTML解析下载: {url}")

        # 设置请求头
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        }

        # 添加Cookie
        if LOFTER_COOKIE_KEY and LOFTER_COOKIE_VALUE:
            headers["Cookie"] = f"{LOFTER_COOKIE_KEY}={LOFTER_COOKIE_VALUE}"

        async with httpx.AsyncClient(timeout=30, follow_redirects=True) as client:
            logger.info("📡 正在获取页面HTML...")
            response = await client.get(url, headers=headers)

            if response.status_code != 200:
                logger.error(f"❌ HTTP请求失败: {response.status_code}")
                return None

            html_content = response.text
            logger.info(f"✅ 获取到HTML内容，长度: {len(html_content)}")

            # 解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')

            # 提取文章标题
            title = extract_title_from_html(soup, url)

            # 提取作者信息
            author = extract_author_from_html(soup, url)

            # 提取文章内容
            content = extract_content_from_html(soup)

            if not content or not content.strip():
                logger.error("❌ 未能从HTML中提取到文章内容")
                return None

            logger.info(f"✅ HTML解析成功 - 标题: {title}, 作者: {author}")

            # 格式化内容
            formatted_content = f"标题: {title}\n作者: {author}\n\n{'-'*50}\n\n{content}"

            return title, formatted_content, author

    except Exception as e:
        logger.error(f"❌ HTML解析下载失败: {e}")
        return None

def extract_title_from_html(soup: BeautifulSoup, url: str) -> str:
    """从HTML中提取文章标题"""
    try:
        # 方法1: 尝试从页面标题提取
        title_tag = soup.find('title')
        if title_tag and title_tag.text:
            title = title_tag.text.strip()
            # 清理标题，移除网站名称等
            if ' - LOFTER' in title:
                title = title.split(' - LOFTER')[0]
            if title and title != 'LOFTER':
                return title

        # 方法2: 尝试从meta标签提取
        meta_title = soup.find('meta', {'property': 'og:title'})
        if meta_title and meta_title.get('content'):
            return meta_title.get('content').strip()

        # 方法3: 尝试从文章内容区域提取
        content_area = soup.find('div', class_='content') or soup.find('div', class_='post')
        if content_area:
            h1_tag = content_area.find('h1')
            if h1_tag and h1_tag.text:
                return h1_tag.text.strip()

        # 方法4: 从URL中提取博客名作为标题
        url_match = re.match(r'https://([^.]+)\.lofter\.com', url)
        if url_match:
            return f"{url_match.group(1)}的文章"

        return "未知标题"

    except Exception as e:
        logger.error(f"提取标题失败: {e}")
        return "未知标题"

def extract_author_from_html(soup: BeautifulSoup, url: str) -> str:
    """从HTML中提取作者信息"""
    try:
        # 方法1: 从URL中提取博客名
        url_match = re.match(r'https://([^.]+)\.lofter\.com', url)
        if url_match:
            blog_name = url_match.group(1)
            return blog_name

        # 方法2: 尝试从meta标签提取
        meta_author = soup.find('meta', {'name': 'author'})
        if meta_author and meta_author.get('content'):
            return meta_author.get('content').strip()

        # 方法3: 尝试从页面中的作者信息提取
        author_elements = soup.find_all(['span', 'div', 'a'], class_=re.compile(r'author|user|blogger'))
        for element in author_elements:
            if element.text and element.text.strip():
                return element.text.strip()

        return "未知作者"

    except Exception as e:
        logger.error(f"提取作者失败: {e}")
        return "未知作者"

def extract_content_from_html(soup: BeautifulSoup) -> str:
    """从HTML中提取文章内容"""
    try:
        content_parts = []

        # 方法1: 查找常见的内容容器
        content_selectors = [
            'div.content',
            'div.post-content',
            'div.entry-content',
            'div.article-content',
            'div.post',
            'article',
            '.txt',
            '.text'
        ]

        content_found = False
        for selector in content_selectors:
            content_elements = soup.select(selector)
            if content_elements:
                for element in content_elements:
                    text = extract_text_from_element(element)
                    if text and len(text.strip()) > 50:  # 过滤太短的内容
                        content_parts.append(text)
                        content_found = True
                        break
                if content_found:
                    break

        # 方法2: 如果没找到，尝试查找所有段落
        if not content_parts:
            paragraphs = soup.find_all(['p', 'div'], string=True)
            for p in paragraphs:
                text = p.get_text().strip()
                if text and len(text) > 20:
                    content_parts.append(text)

        # 方法3: 最后尝试提取所有文本内容
        if not content_parts:
            # 移除脚本和样式标签
            for script in soup(["script", "style", "nav", "header", "footer"]):
                script.decompose()

            body = soup.find('body')
            if body:
                text = body.get_text()
                # 清理文本
                lines = [line.strip() for line in text.splitlines() if line.strip()]
                content_parts = [line for line in lines if len(line) > 10]

        if content_parts:
            content = '\n\n'.join(content_parts)
            # 清理内容
            content = clean_html_content(content)
            return content

        return ""

    except Exception as e:
        logger.error(f"提取内容失败: {e}")
        return ""

def extract_text_from_element(element) -> str:
    """从HTML元素中提取纯文本"""
    try:
        # 移除不需要的标签
        for tag in element.find_all(['script', 'style', 'nav', 'header', 'footer']):
            tag.decompose()

        # 获取文本内容
        text = element.get_text()

        # 清理文本
        lines = [line.strip() for line in text.splitlines() if line.strip()]
        return '\n'.join(lines)

    except Exception as e:
        logger.error(f"提取元素文本失败: {e}")
        return ""

def clean_html_content(content: str) -> str:
    """清理HTML内容"""
    if not content:
        return ""

    # HTML解码
    content = html.unescape(content)

    # 移除多余的空白行
    lines = [line.strip() for line in content.splitlines() if line.strip()]
    content = '\n'.join(lines)

    # 移除重复的换行
    content = re.sub(r'\n{3,}', '\n\n', content)

    return content.strip()

async def download_lofter_collection(collection_url: str):
    """下载Lofter合集"""
    try:
        logger.info(f"📚 开始下载合集: {collection_url}")

        # 先处理HTML实体编码
        collection_url = html.unescape(collection_url)
        logger.info(f"📚 处理后的URL: {collection_url}")

        # 解析合集URL获取collectionId
        import urllib.parse as urlparse
        parsed_url = urlparse.urlparse(collection_url)
        query_params = urlparse.parse_qs(parsed_url.query)

        if 'collectionId' not in query_params:
            logger.error("❌ 无法从URL中提取合集ID")
            return None

        collection_id = query_params['collectionId'][0]
        logger.info(f"🆔 合集ID: {collection_id}")

        # 设置请求头
        headers = {
            "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            LOFTER_COOKIE_KEY: LOFTER_COOKIE_VALUE
        }

        # 先通过搜索API获取合集信息
        collection_info = await get_collection_info(collection_id, headers)
        if not collection_info:
            logger.error("❌ 无法获取合集信息")
            return None

        # 获取合集中的文章列表
        collection_posts = await get_collection_posts_by_id(collection_id, collection_info, headers)
        if not collection_posts:
            logger.error("❌ 无法获取合集文章列表")
            return None

        logger.info(f"📖 合集包含 {len(collection_posts)} 篇文章")

        # 合并所有文章为一个文件
        collection_title = collection_info.get('name', '未知合集')
        collection_author = collection_info.get('blogNickName', '未知作者')

        content_parts = []
        content_parts.append(f"合集标题: {collection_title}")
        content_parts.append(f"创建者: {collection_author}")
        content_parts.append(f"文章数量: {len(collection_posts)}")
        content_parts.append(f"下载时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        content_parts.append("=" * 80)
        content_parts.append("")

        success_count = 0
        for i, post in enumerate(collection_posts, 1):
            try:
                if 'post' not in post or not isinstance(post['post'], dict):
                    continue

                post_data = post['post']
                title = post_data.get('title', post_data.get('digest', f'文章{i}'))
                author = post_data.get('blogInfo', {}).get('blogNickName', '未知作者')

                logger.info(f"📖 [{i}/{len(collection_posts)}] 处理文章: {title}")

                # 提取文章内容
                article_content = extract_post_content_from_data(post_data)
                if article_content:
                    content_parts.append(f"第 {i} 篇文章")
                    content_parts.append(f"标题: {title}")
                    content_parts.append(f"作者: {author}")
                    content_parts.append("-" * 60)
                    content_parts.append(article_content)
                    content_parts.append("")
                    content_parts.append("=" * 80)
                    content_parts.append("")
                    success_count += 1
                else:
                    logger.warning(f"❌ 无法提取文章内容: {title}")

            except Exception as e:
                logger.error(f"❌ 处理文章失败: {e}")
                continue

        if success_count > 0:
            # 合并所有内容
            full_content = '\n'.join(content_parts)
            logger.info(f"✅ 合集下载完成！成功处理 {success_count}/{len(collection_posts)} 篇文章")
            return f"【合集】{collection_title}", full_content, collection_author
        else:
            logger.error("❌ 没有成功处理任何文章")
            return None

    except Exception as e:
        logger.error(f"❌ 合集下载失败: {e}")
        return None

async def get_collection_info(collection_id: str, headers: dict):
    """获取合集基本信息"""
    max_retries = 3
    retry_delay = 2

    for attempt in range(max_retries):
        try:
            search_url = f"https://api.lofter.com/newsearch/collection.json?key={collection_id}&limit=20&offset=0"

            logger.info(f"🔍 搜索合集信息 (尝试 {attempt + 1}/{max_retries}): {search_url}")

            async with httpx.AsyncClient(timeout=30, follow_redirects=True) as client:
                response = await client.get(search_url, headers=headers)

            if response.status_code != 200:
                logger.error(f"❌ 搜索合集失败，状态码: {response.status_code}")
                if attempt < max_retries - 1:
                    logger.info(f"⏳ {retry_delay}秒后重试...")
                    await asyncio.sleep(retry_delay)
                    continue
                return None

            try:
                result = response.json()
                logger.info(f"📊 搜索响应结构: {list(result.keys()) if isinstance(result, dict) else type(result)}")

                # 检查不同层级的collections字段
                collections = None
                if 'data' in result and 'collections' in result['data']:
                    collections = result['data']['collections']
                    logger.info(f"🔍 data.collections字段: 类型={type(collections)}, 长度={len(collections) if collections else 0}")
                elif 'collections' in result:
                    collections = result['collections']
                    logger.info(f"🔍 collections字段: 类型={type(collections)}, 长度={len(collections) if collections else 0}")

                if collections and len(collections) > 0:
                    logger.info(f"✅ 找到 {len(collections)} 个合集")

                    # 显示所有找到的合集
                    for i, collection in enumerate(collections):
                        logger.info(f"  合集{i+1}: ID={collection.get('id')}, 名称={collection.get('name', '未知')}")

                    # 查找匹配的合集
                    target_collection = None
                    for collection in collections:
                        if str(collection.get('id', '')) == collection_id:
                            target_collection = collection
                            logger.info(f"✅ 找到ID匹配的合集: {collection.get('name', '未知合集')}")
                            break

                    if not target_collection and collections:
                        # 如果没有精确匹配，使用第一个结果
                        target_collection = collections[0]
                        logger.warning("⚠️ 没有ID匹配，使用第一个搜索结果")

                    if target_collection:
                        logger.info(f"✅ 返回合集信息")
                        logger.info(f"📝 合集名称: {target_collection.get('name', '未知合集')}")
                        logger.info(f"👤 创建者: {target_collection.get('blogNickName', '未知作者')}")
                        logger.info(f"📊 文章数量: {target_collection.get('postCount', 0)}")
                        logger.info(f"🏠 博客ID: {target_collection.get('blogId', 'N/A')}")
                        logger.info(f"🌐 博客名: {target_collection.get('blogName', 'N/A')}")
                        return target_collection
                    else:
                        logger.error("❌ 未找到匹配的合集")
                        return None
                else:
                    logger.error("❌ 搜索结果中没有合集数据或合集列表为空")
                    if 'data' in result:
                        data = result['data']
                        logger.error(f"💡 data结构: {list(data.keys()) if isinstance(data, dict) else data}")
                        if 'collections' in data:
                            logger.error(f"💡 data.collections内容: {data['collections']}")
                    if 'collections' in result:
                        logger.error(f"💡 root.collections内容: {result['collections']}")
                    logger.error(f"💡 完整响应结构: {list(result.keys())}")

                    # 尝试通过合集名称搜索作为备选方案
                    logger.info("🔄 尝试通过合集名称搜索...")
                    name_result = await search_collection_by_name("ⅰf线之百变江晚吟", headers)
                    if name_result:
                        logger.info("✅ 通过名称搜索找到合集")
                        return name_result

                    return None

            except Exception as e:
                logger.error(f"❌ 解析搜索响应失败: {e}")
                logger.error(f"响应内容: {response.text[:200]}...")
                if attempt < max_retries - 1:
                    logger.info(f"⏳ {retry_delay}秒后重试...")
                    await asyncio.sleep(retry_delay)
                    continue
                return None

        except Exception as e:
            logger.error(f"❌ 获取合集信息异常 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                logger.info(f"⏳ {retry_delay}秒后重试...")
                await asyncio.sleep(retry_delay)
                continue
            return None

    logger.error(f"❌ 所有重试都失败了，无法获取合集信息")
    return None

async def search_collection_by_name(collection_name: str, headers: dict):
    """通过合集名称搜索合集"""
    max_retries = 3
    retry_delay = 2

    for attempt in range(max_retries):
        try:
            logger.info(f"🔍 通过名称搜索合集 (尝试 {attempt + 1}/{max_retries}): {collection_name}")

            # 使用合集搜索API
            from urllib.parse import quote
            search_url = f"https://api.lofter.com/newsearch/collection.json?key={quote(collection_name)}&limit=20&offset=0"

            async with httpx.AsyncClient(timeout=30, follow_redirects=True) as client:
                response = await client.get(search_url, headers=headers)

                if response.status_code != 200:
                    logger.error(f"❌ 搜索失败，状态码: {response.status_code}")
                    if attempt < max_retries - 1:
                        logger.info(f"⏳ {retry_delay}秒后重试...")
                        await asyncio.sleep(retry_delay)
                        continue
                    return None

                try:
                    data = response.json()
                    if 'data' in data and 'collections' in data['data'] and data['data']['collections']:
                        collections = data['data']['collections']

                        # 查找最匹配的合集
                        best_match = None
                        best_score = 0

                        for collection in collections:
                            name = collection.get('name', '')
                            # 计算匹配度
                            if collection_name.lower() in name.lower():
                                score = len(collection_name) / len(name) if name else 0
                                if score > best_score:
                                    best_score = score
                                    best_match = collection

                        if best_match:
                            logger.info(f"✅ 找到匹配合集: {best_match.get('name', '未知合集')}")
                            return best_match
                        else:
                            logger.error("❌ 未找到匹配的合集")
                            return None
                    else:
                        logger.error("❌ 搜索结果中没有合集")
                        return None

                except Exception as e:
                    logger.error(f"❌ 搜索响应解析失败: {e}")
                    if attempt < max_retries - 1:
                        logger.info(f"⏳ {retry_delay}秒后重试...")
                        await asyncio.sleep(retry_delay)
                        continue
                    return None

        except Exception as e:
            logger.error(f"❌ 通过名称搜索合集失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                logger.info(f"⏳ {retry_delay}秒后重试...")
                await asyncio.sleep(retry_delay)
                continue
            return None

    logger.error(f"❌ 所有重试都失败了，无法通过名称搜索合集")
    return None

async def get_collection_posts_by_id(collection_id: str, collection_info: dict, headers: dict):
    """通过合集ID获取合集中的文章列表"""
    try:
        if not collection_info:
            logger.error("❌ 需要合集信息来获取文章列表")
            return None

        # 从合集信息中获取必要参数
        blog_id = collection_info.get('blogId')
        blog_name = collection_info.get('blogName')

        if not blog_id or not blog_name:
            logger.error("❌ 合集信息中缺少博客ID或博客名")
            return None

        logger.info(f"📚 获取合集文章: blogId={blog_id}, blogName={blog_name}")

        # 根据API配置使用正确的API
        api_url = "https://api.lofter.com/v1.1/postCollection.api?product=lofter-android-7.4.4"

        # 构建请求体
        post_data = {
            "blogdomain": f"{blog_name}.lofter.com",
            "method": "getCollectionSimple",
            "offset": "0",
            "limit": "2000",
            "blogid": str(blog_id),
            "collectionid": str(collection_id),
            "order": "1"
        }

        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.post(api_url, data=post_data, headers=headers)

            if response.status_code != 200:
                logger.error(f"❌ 获取合集文章失败，状态码: {response.status_code}")
                return None

            try:
                result = response.json()

                # 根据实际API响应，文章列表在 response.items 中
                if 'response' in result and 'items' in result['response']:
                    items = result['response']['items']

                    if items:
                        logger.info(f"✅ 成功获取合集中的 {len(items)} 篇文章")
                        return items
                    else:
                        logger.error("❌ 合集中没有文章（items为空）")
                        return None
                elif 'response' in result and 'posts' in result['response']:
                    # 备选：有些API可能返回posts字段
                    posts = result['response']['posts']
                    if posts:
                        logger.info(f"✅ 成功获取合集中的 {len(posts)} 篇文章")
                        return posts
                    else:
                        logger.error("❌ 合集中没有文章")
                        return None
                else:
                    logger.error("❌ 合集文章响应格式错误")
                    return None

            except Exception as e:
                logger.error(f"❌ 解析合集文章响应失败: {e}")
                return None

    except Exception as e:
        logger.error(f"❌ 获取合集文章异常: {e}")
        return None

def extract_post_content_from_data(post_data: dict) -> str:
    """从文章数据中提取内容"""
    try:
        if not post_data:
            return None

        # 处理嵌套的post结构
        if 'post' in post_data and isinstance(post_data['post'], dict):
            actual_post = post_data['post']
        else:
            actual_post = post_data

        # 提取基本信息
        content = actual_post.get('content', '')

        # 如果没有content字段，尝试其他字段
        if not content:
            content = actual_post.get('digest', '')

        if not content:
            return None

        # 清理HTML标签
        content = clean_html_content(content)

        if not content.strip():
            return None

        return content

    except Exception as e:
        logger.error(f"❌ 提取文章内容失败: {e}")
        return None





async def update_download_status(user_id: int, title: str, total_articles: int):
    """更新下载状态"""
    if user_id not in download_status:
        download_status[user_id] = []

    download_status[user_id].append({
        'title': title,
        'total_articles': total_articles,
        'current_article': 0,
        'progress': f"《{title}》 (0/{total_articles})"
    })

def clear_download_status(user_id: int, title: str):
    """清理下载状态"""
    if user_id in download_status:
        download_status[user_id] = [p for p in download_status[user_id] if p['title'] != title]
        if not download_status[user_id]:
            del download_status[user_id]

async def get_folder_id(bot: Bot, group_id: int, folder_name: str):
    """获取群文件夹ID"""
    try:
        jsonDate = await bot.call_api("get_group_root_files", group_id=group_id)
        for folder in jsonDate.get("folders", []):
            if folder_name == folder["folder_name"]:
                return folder["folder_id"]
        return await create_folder(bot, group_id, folder_name)
    except Exception as e:
        logger.error(f"获取群文件夹ID失败: {e}")
        return ""

async def create_folder(bot: Bot, group_id: int, folder_name: str):
    """创建群文件夹"""
    try:
        jsonDate = await bot.call_api("create_group_file_folder", group_id=group_id, folder_name=folder_name)
        return jsonDate["folder_id"]
    except Exception as e:
        logger.error(f"创建群文件夹失败: {e}")
        return ""

async def upload_to_webdav(file_path, file_name):
    """上传到WebDAV"""
    try:
        with open(file_path, 'rb') as f:
            file_content = f.read()

        remote_path = f"{WEBDAV_FOLDER}/{file_name}"
        webdav_url = f"{WEBDAV_URL}/{remote_path}"
        auth = (WEBDAV_USERNAME, WEBDAV_PASSWORD)

        async with httpx.AsyncClient(auth=auth, verify=False) as client:
            response = await client.put(webdav_url, content=file_content, timeout=60)

            if response.status_code in (200, 201, 204):
                base_url = WEBDAV_URL.split('/dav')[0]
                download_url = f"{base_url}/琪露诺上传的《小说》/{file_name}"
                logger.info(f"WebDAV上传成功: {download_url}")
                return download_url
            else:
                logger.error(f"WebDAV上传失败: {response.status_code}")
                return None
    except Exception as e:
        logger.error(f"WebDAV上传错误: {e}")
        return None
