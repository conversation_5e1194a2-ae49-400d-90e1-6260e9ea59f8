import requests
import json
import time
import os

# 设置浏览器ID
browerid = "6884965a390fea598af8b071"

brurl = 'http://127.0.0.1:3001/auto/openBrower'
headers = {
    'Content-Type': 'application/json'
}
payload = json.dumps({
    "browerid": browerid
})

project_response = requests.request("POST", brurl, headers=headers, data=payload)
time.sleep(22)
os.system('taskkill /im chrome.exe /F')
time.sleep(1)
project_response = requests.request("POST", brurl, headers=headers, data=payload)
time.sleep(22)
os.system('taskkill /im chrome.exe /F')
time.sleep(1)

sql3dburl = f"http://127.0.0.1:3001/sql3db/sql3GetBrUn?browerid={browerid}"

projec = requests.request("GET", sql3dburl, headers=headers)

print(projec.text)
if "cookie" in projec.text and projec.status_code == 200:
    protext = projec.text

    projson = json.loads(protext)
    if projson.get("code") == 0 and "data" in projson:
        prodata = projson["data"]
        print("浏览器数据获取成功:")
        print(f"浏览器ID: {prodata.get('browerid')}")
        print(f"浏览器名称: {prodata.get('browername')}")
        print(f"网站URL: {prodata.get('siteurl')}")

        cookie = prodata.get('cookie')
        if cookie:
            print(f"\n获取到的cookie数据:")
            # 解析cookie JSON字符串
            cookie_list = json.loads(cookie)
            print(f"Cookie数量: {len(cookie_list)}")

            # 显示前几个cookie作为示例
            for i, cookie_item in enumerate(cookie_list[:5]):
                print(f"Cookie {i+1}: {cookie_item.get('name')} = {cookie_item.get('value')[:50]}...")

            if len(cookie_list) > 5:
                print(f"... 还有 {len(cookie_list) - 5} 个cookie")
        else:
            print("cookie数据为空")
    else:
        print(f"API返回错误: {projson.get('desc', '未知错误')}")
else:
    print(f"请求失败，状态码: {projec.status_code}")
    print("未找到有效的cookie数据")