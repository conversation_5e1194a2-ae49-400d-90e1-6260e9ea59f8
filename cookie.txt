 brurl = 'http://127.0.0.1:3001/auto/openBrower'
        headers = {
            'Content-Type': 'application/json'
        }
        payload = json.dumps({
            "browerid": browerid

        })
        project_response = requests.request("POST", brurl, headers=headers, data=payload)
        time.sleep(22)
        os.system('taskkill /im chrome.exe /F')
        time.sleep(1)
        project_response = requests.request("POST", brurl, headers=headers, data=payload)
        time.sleep(22)
        os.system('taskkill /im chrome.exe /F')
        time.sleep(1)

        sql3dburl = f"http://127.0.0.1:3001/sql3db/sql3GetBrUn?browerid={browerid}"

        projec = requests.request("GET", sql3dburl, headers=headers)

        print(projec.text)
        if "BEC" in projec.text and "cookie" in projec.text:
            protext = projec.text

            projson = json.loads(protext)
            prodata = projson["data"]
            print(prodata)
            cookie = prodata['cookie']